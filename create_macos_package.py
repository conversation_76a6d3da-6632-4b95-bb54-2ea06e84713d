#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建macOS构建包
将所有必要文件打包成一个可以在macOS上构建的完整包
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_macos_build_package():
    """创建macOS构建包"""
    
    print("🚀 创建macOS构建包...")
    
    # 创建临时目录
    package_dir = Path("ShopSetup_macOS_BuildPackage")
    if package_dir.exists():
        shutil.rmtree(package_dir)
    package_dir.mkdir()
    
    # 需要包含的文件列表
    files_to_include = [
        # 核心源码文件
        "script/shopSetup.py",
        "ziniao_rpa_base.py",
        "gui_framework.py", 
        "gui_utils.py",
        "global_logger.py",
        "ziniao_socket_client.py",
        "ziniao_config.json",
        
        # 构建脚本
        "build_cross_platform.py",
        "build_macos.sh",
        "requirements-build.txt",
        
        # 文档
        "BUILD_GUIDE.md",
        "macOS_Build_Package.md"
    ]
    
    # 复制文件
    print("📁 复制必要文件...")
    for file_path in files_to_include:
        src = Path(file_path)
        if src.exists():
            # 保持目录结构
            dst = package_dir / file_path
            dst.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(src, dst)
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ 警告: 找不到文件 {file_path}")
    
    # 创建macOS专用的README
    macos_readme = package_dir / "README_macOS.txt"
    with open(macos_readme, 'w', encoding='utf-8') as f:
        f.write("""店铺设置工具 - macOS构建包
============================

这是一个完整的macOS构建包，包含在Mac上生成可执行文件所需的所有文件。

🚀 快速开始：

1. 确保您的Mac满足系统要求：
   - macOS 10.14 或更高版本
   - Python 3.8+ (推荐使用 Homebrew 安装)

2. 安装Python (如果未安装)：
   brew install python3

3. 给予构建脚本执行权限：
   chmod +x build_macos.sh

4. 运行一键构建：
   ./build_macos.sh

5. 构建完成后，您将得到 ShopSetup_macOS 文件夹，
   其中包含可在Mac上运行的可执行文件。

📋 详细说明请查看：
- BUILD_GUIDE.md - 完整构建指南
- macOS_Build_Package.md - macOS专用说明

🔧 如遇问题：
1. 确认Python版本：python3 --version
2. 安装依赖：pip3 install PyInstaller
3. 查看详细错误信息

更新日期: 2024-12-01
版本: 2.0
""")
    
    # 创建一键构建脚本的副本（确保权限）
    build_script = package_dir / "build_macos.sh"
    if build_script.exists():
        # 在Unix系统上设置执行权限（如果当前是Unix系统）
        try:
            os.chmod(build_script, 0o755)
            print("   ✅ 设置build_macos.sh执行权限")
        except:
            print("   ⚠️ 无法设置执行权限（Windows环境）")
    
    # 创建压缩包
    zip_path = "ShopSetup_macOS_BuildPackage.zip"
    print(f"📦 创建压缩包: {zip_path}")
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(package_dir)
                zipf.write(file_path, arc_path)
                print(f"   📄 添加: {arc_path}")
    
    # 清理临时目录
    shutil.rmtree(package_dir)
    
    # 显示结果
    zip_size = os.path.getsize(zip_path) / 1024 / 1024  # MB
    print()
    print("🎉 macOS构建包创建完成！")
    print(f"📦 文件: {zip_path}")
    print(f"📊 大小: {zip_size:.1f} MB")
    print()
    print("📋 使用说明：")
    print("1. 将此压缩包发送到Mac电脑")
    print("2. 解压缩到任意目录")
    print("3. 在终端中运行: chmod +x build_macos.sh")
    print("4. 执行构建: ./build_macos.sh")
    print("5. 等待构建完成，获得macOS可执行文件")
    print()
    
    return zip_path

if __name__ == "__main__":
    try:
        zip_path = create_macos_build_package()
        print(f"✅ 成功创建macOS构建包: {zip_path}")
    except Exception as e:
        print(f"❌ 创建构建包失败: {e}")
        import traceback
        traceback.print_exc()
