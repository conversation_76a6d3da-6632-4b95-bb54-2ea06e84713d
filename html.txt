<div id="DOMESTIC" class="a-box service_type_group_box"><div class="a-box-inner">

    <div class="a-section service_type_group_name_section serviceTypeGroupMetadata">
        <span class="a-size-large checkbox_label a-text-bold">
            国内配送
        </span>
        <span class="checkbox_label">

        </span>
    </div>

    <div class="a-section service_type_group_section configRuleGroups">
    <div><div><div id="JP_STANDARD.DOMESTIC" class="a-box-group a-spacing-top-base ">
    <div class="a-box a-box-title a-text-left serviceTypeBox"><div class="a-box-inner">
        <div class="a-fixed-right-grid"><div class="a-fixed-right-grid-inner" style="padding-right:300px">
            <div class="a-fixed-right-grid-col a-col-left" style="padding-right:3.5%;float:left;">



                            <div data-a-input-name="service_type" class="a-checkbox service_type_checkbox"><label><input type="checkbox" name="service_type" value="JP_STANDARD.DOMESTIC" checked="" disabled=""><i class="a-icon a-icon-checkbox"></i><span class="a-label a-checkbox-label"></span><div class="overlay a-declarative" style="display: inline; position: absolute; top: 2.95312px; left: 0px; width: 13px; height: 13px; background-color: rgb(255, 255, 255); opacity: 0;"></div></label></div>



                <div style="display: inline-block; vertical-align: top;">
                    <span class="a-size-medium checkbox_label a-text-bold">
                        标准配送


                    </span>

                        <div class="service_type_header_shipmethods"></div>

                </div>


                <span id="handlingTime" class="service_type_header_tips">   </span>


                <span class="constraintMessageNode"></span>
                <div class="a-section errorMessageSection">
                    <div id="JP_STANDARD.DOMESTIC_dependency_rule_error_message" class="a-box a-alert-inline a-alert-inline-error dependency_rule_error_message error_message" role="alert"><div class="a-box-inner a-alert-container"><i class="a-icon a-icon-alert" aria-hidden="true"></i><div class="a-alert-content"></div></div></div>
                </div>
            </div>
            <div class="a-text-right a-fixed-right-grid-col prime_eligibility_text_for_IN a-col-right" style="width:300px;margin-right:-300px;float:left;">

            </div>
        </div></div>
    </div></div>







  <div id="customized_delivery_rules_box~JP_STANDARD.DOMESTIC"></div>

  <div class="a-box-group scheduledTimeWindowBox ">
    <div class="a-box a-box-normal scheduledTimeWindowSettings"><div class="a-box-inner a-padding-none">
    </div></div>
  </div>




 <div class="a-box-group configRulesBox ">


    <div class="a-box a-box-title a-text-left addRegionsBox" style="display: none;"><div class="a-box-inner">
      <div class="a-section sbr_fixed_column_row">
        <span></span>
        <div class="a-section a-spacing-none sbr_fixed_column requirementsMessageBox">
        </div>
        <div class="a-section a-spacing-none sbr_fixed_column alertMessageBox">
        </div>
        <br>
        <div class="a-section a-spacing-none sbr_fixed_column warningMessageBox">
        </div>
      </div>

    </div></div>

    <spamanual-transittime service-type="JP_STANDARD.DOMESTIC"><div></div></spamanual-transittime>
     <div class="a-box a-box-normal configRulesContent"><div class="a-box-inner a-padding-none">
      <table class="a-bordered a-vertical-stripes configRulesTable">
        <tbody><tr class="heading"><th class="a-text-left a-align-center primeColumn">
            符合 Prime 资格
          </th>

          <th class="a-text-left a-align-center regionsColumn">
            区域
          </th>


          <th class="a-text-left a-align-center shippingTimeColumn">




            <span>运输时间</span>
            <br>
            <span class="shippingTimeDesc">

            不包括备货时间

            </span>
          </th>


          <th class="a-text-left a-align-center shippingFeeColumn_shipment_based">
            运费






<br>





          </th>


          <th class="a-text-left a-align-center actionsColumn">

            <div class="a-section a-spacing-none">
              <span class="actions">操作</span>
            </div>

          </th></tr>
      <tr class="nonPrimeRule"><td class="a-text-left a-align-top primeColumn">
    <div class="a-section prime_section">

    </div>
</td>

<td class="a-align-top">

  <div class="a-row a-grid-vertical-align a-grid-center regions_name">
    <div class="a-section regions_name_left">







                <span>北海道</span>


    </div>

    <div class="a-section regions_name_right">
      <a id="edit_0" class="a-link-normal editRegions" href="javascript:void(0)">编辑</a>
    </div>

  </div>
</td>





<td class="a-text-left a-align-top shippingTime">


  <span class="a-dropdown-container"><select name="shippingTime" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


    <option value="3-11D">
      3 - 11 天
    </option>



    <option value="4-7D">
      4 - 7 天
    </option>



    <option value="7-10D">
      7 - 10 天
    </option>



    <option value="10-15D" selected="">
      10 - 15 天
    </option>


  </select><span tabindex="-1" data-a-class="shippingTime" class="a-button a-button-dropdown shippingTime" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">10 - 15 天</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>


</td>















<td class="shippingFee">








<div name="shippingFeePerOrderDiv">
    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>


                 <input type="text" value="0" name="pricePerOrder" class="a-input-text">


    </div></div>
    <span>每个订单</span>
</div>

<div name="shippingFeePlusDiv" class="aok-block">
    <div class="a-divider a-divider-break a-spacing-medium a-spacing-top-medium"><h5 aria-level="5">加</h5></div>

    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>

        <input type="text" value="0" name="unitPrice" class="a-input-text">

    </div></div>



        <span> 每  </span>



      <span class="a-dropdown-container"><select name="unitMeasure" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative" aria-pressed="false">


        <option value="Per Item"> 商品 </option>



        <option value="Per Kilo" selected=""> Kg </option>


      </select><span tabindex="-1" data-a-class="unitMeasure" class="a-button a-button-dropdown unitMeasure" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">Kg</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>



</div>

</td>











<td class="a-text-left a-align-top">

  <a id="delete_0" class="a-link-normal deleteConfigRule" href="javascript:void(0)">
    删除
  </a>

</td></tr><tr class="nonPrimeRule"><td class="a-text-left a-align-top primeColumn">
    <div class="a-section prime_section">

    </div>
</td>

<td class="a-align-top">

  <div class="a-row a-grid-vertical-align a-grid-center regions_name">
    <div class="a-section regions_name_left">







                <span>广岛县, 冈山县, 岛根县, 鸟取县, 山口县, 爱知县, 岐阜县, 三重县, 静冈县, 长野县, 新泻县, 秋田县, 青森县, 岩手县, 福井县, 石川县, 富山县, 福岛县, 宫城县, 山形县, 千叶县, 群马县, 茨城县, 神奈川县, 埼玉县, 栃木县, 东京都, 山梨县, 兵库县, 京都府, 奈良县, 大阪府, 滋贺县, 和歌山县</span>


    </div>

    <div class="a-section regions_name_right">
      <a id="edit_1" class="a-link-normal editRegions" href="javascript:void(0)">编辑</a>
    </div>

  </div>
</td>





<td class="a-text-left a-align-top shippingTime">


  <span class="a-dropdown-container"><select name="shippingTime" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


    <option value="3-11D">
      3 - 11 天
    </option>



    <option value="4-7D">
      4 - 7 天
    </option>



    <option value="7-10D">
      7 - 10 天
    </option>



    <option value="10-15D" selected="">
      10 - 15 天
    </option>


  </select><span tabindex="-1" data-a-class="shippingTime" class="a-button a-button-dropdown shippingTime" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">10 - 15 天</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>


</td>















<td class="shippingFee">








<div name="shippingFeePerOrderDiv">
    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>


                 <input type="text" value="0" name="pricePerOrder" class="a-input-text">


    </div></div>
    <span>每个订单</span>
</div>

<div name="shippingFeePlusDiv" class="aok-block">
    <div class="a-divider a-divider-break a-spacing-medium a-spacing-top-medium"><h5 aria-level="5">加</h5></div>

    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>

        <input type="text" value="0" name="unitPrice" class="a-input-text">

    </div></div>



        <span> 每  </span>



      <span class="a-dropdown-container"><select name="unitMeasure" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


        <option value="Per Item"> 商品 </option>



        <option value="Per Kilo" selected=""> Kg </option>


      </select><span tabindex="-1" data-a-class="unitMeasure" class="a-button a-button-dropdown unitMeasure" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">Kg</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>



</div>

</td>











<td class="a-text-left a-align-top">

  <a id="delete_1" class="a-link-normal deleteConfigRule" href="javascript:void(0)">
    删除
  </a>

</td></tr><tr class="nonPrimeRule"><td class="a-text-left a-align-top primeColumn">
    <div class="a-section prime_section">

    </div>
</td>

<td class="a-align-top">

  <div class="a-row a-grid-vertical-align a-grid-center regions_name">
    <div class="a-section regions_name_left">







                <span>福冈县, 鹿儿岛县, 熊本县, 宫崎县, 长崎县, 大分县, 佐贺县, 爱媛县, 香川县, 高知县, 德岛县</span>


    </div>

    <div class="a-section regions_name_right">
      <a id="edit_2" class="a-link-normal editRegions" href="javascript:void(0)">编辑</a>
    </div>

  </div>
</td>





<td class="a-text-left a-align-top shippingTime">


  <span class="a-dropdown-container"><select name="shippingTime" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


    <option value="3-11D">
      3 - 11 天
    </option>



    <option value="4-7D">
      4 - 7 天
    </option>



    <option value="7-10D">
      7 - 10 天
    </option>



    <option value="10-15D" selected="">
      10 - 15 天
    </option>


  </select><span tabindex="-1" data-a-class="shippingTime" class="a-button a-button-dropdown shippingTime" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">10 - 15 天</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>


</td>















<td class="shippingFee">








<div name="shippingFeePerOrderDiv">
    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>


                 <input type="text" value="0" name="pricePerOrder" class="a-input-text">


    </div></div>
    <span>每个订单</span>
</div>

<div name="shippingFeePlusDiv" class="aok-block">
    <div class="a-divider a-divider-break a-spacing-medium a-spacing-top-medium"><h5 aria-level="5">加</h5></div>

    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>

        <input type="text" value="0" name="unitPrice" class="a-input-text">

    </div></div>



        <span> 每  </span>



      <span class="a-dropdown-container"><select name="unitMeasure" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


        <option value="Per Item"> 商品 </option>



        <option value="Per Kilo" selected=""> Kg </option>


      </select><span tabindex="-1" data-a-class="unitMeasure" class="a-button a-button-dropdown unitMeasure" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">Kg</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>



</div>

</td>











<td class="a-text-left a-align-top">

  <a id="delete_2" class="a-link-normal deleteConfigRule" href="javascript:void(0)">
    删除
  </a>

</td></tr><tr class="nonPrimeRule"><td class="a-text-left a-align-top primeColumn">
    <div class="a-section prime_section">

    </div>
</td>

<td class="a-align-top">

  <div class="a-row a-grid-vertical-align a-grid-center regions_name">
    <div class="a-section regions_name_left">







                <span>冲绳县, 冲绳离岛</span>


    </div>

    <div class="a-section regions_name_right">
      <a id="edit_3" class="a-link-normal editRegions" href="javascript:void(0)">编辑</a>
    </div>

  </div>
</td>





<td class="a-text-left a-align-top shippingTime">


  <span class="a-dropdown-container"><select name="shippingTime" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


    <option value="3-11D">
      3 - 11 天
    </option>



    <option value="4-7D">
      4 - 7 天
    </option>



    <option value="7-10D">
      7 - 10 天
    </option>



    <option value="10-15D" selected="">
      10 - 15 天
    </option>


  </select><span tabindex="-1" data-a-class="shippingTime" class="a-button a-button-dropdown shippingTime" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">10 - 15 天</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>


</td>















<td class="shippingFee">








<div name="shippingFeePerOrderDiv">
    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>


                 <input type="text" value="0" name="pricePerOrder" class="a-input-text">


    </div></div>
    <span>每个订单</span>
</div>

<div name="shippingFeePlusDiv" class="aok-block">
    <div class="a-divider a-divider-break a-spacing-medium a-spacing-top-medium"><h5 aria-level="5">加</h5></div>

    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>

        <input type="text" value="3199" name="unitPrice" class="a-input-text">

    </div></div>



        <span> 每  </span>



      <span class="a-dropdown-container"><select name="unitMeasure" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


        <option value="Per Item"> 商品 </option>



        <option value="Per Kilo" selected=""> Kg </option>


      </select><span tabindex="-1" data-a-class="unitMeasure" class="a-button a-button-dropdown unitMeasure" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">Kg</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>



</div>

</td>











<td class="a-text-left a-align-top">

  <a id="delete_3" class="a-link-normal deleteConfigRule" href="javascript:void(0)">
    删除
  </a>

</td></tr><tr class="nonPrimeRule last_child"><td class="a-text-left a-align-top primeColumn">
    <div class="a-section prime_section">

    </div>
</td>

<td class="a-align-top">

  <div class="a-row a-grid-vertical-align a-grid-center regions_name">
    <div class="a-section regions_name_left">







                <span>东离岛, 偏远离岛, 北海道离岛, 西离岛</span>


    </div>

    <div class="a-section regions_name_right">
      <a id="edit_4" class="a-link-normal editRegions" href="javascript:void(0)">编辑</a>
    </div>

  </div>
</td>





<td class="a-text-left a-align-top shippingTime">


  <span class="a-dropdown-container"><select name="shippingTime" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


    <option value="3-11D">
      3 - 11 天
    </option>



    <option value="4-7D">
      4 - 7 天
    </option>



    <option value="7-10D">
      7 - 10 天
    </option>



    <option value="10-15D" selected="">
      10 - 15 天
    </option>


  </select><span tabindex="-1" data-a-class="shippingTime" class="a-button a-button-dropdown shippingTime" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">10 - 15 天</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>


</td>















<td class="shippingFee">








<div name="shippingFeePerOrderDiv">
    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>


                 <input type="text" value="0" name="pricePerOrder" class="a-input-text">


    </div></div>
    <span>每个订单</span>
</div>

<div name="shippingFeePlusDiv" class="aok-block">
    <div class="a-divider a-divider-break a-spacing-medium a-spacing-top-medium"><h5 aria-level="5">加</h5></div>

    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>

        <input type="text" value="0" name="unitPrice" class="a-input-text">

    </div></div>



        <span> 每  </span>



      <span class="a-dropdown-container"><select name="unitMeasure" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


        <option value="Per Item"> 商品 </option>



        <option value="Per Kilo" selected=""> Kg </option>


      </select><span tabindex="-1" data-a-class="unitMeasure" class="a-button a-button-dropdown unitMeasure" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">Kg</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>



</div>

</td>











<td class="a-text-left a-align-top">

  <a id="delete_4" class="a-link-normal deleteConfigRule" href="javascript:void(0)">
    删除
  </a>

</td></tr></tbody></table>
    </div></div>


    <div class="a-box addNewRuleButtonContainer"><div class="a-box-inner"><span id="JP_STANDARD.DOMESTIC_addRuleButton" class="a-button a-button-primary addRegions a-button-disabled"><span class="a-button-inner"><button id="JP_STANDARD.DOMESTIC_addRuleButton-announce" class="a-button-text" type="button" disabled="">
    添加新区域
  </button></span></span><div class="overlay a-declarative" style="display: inline; position: absolute; top: 14px; left: 18.0006px; width: 88px; height: 31px; background-color: rgb(255, 255, 255); opacity: 0;"></div></div></div>

  </div>





</div></div><div><div id="JP_NEXT_DAY.DOMESTIC" class="a-box-group a-spacing-top-base ">
    <div class="a-box a-box-title a-text-left serviceTypeBox"><div class="a-box-inner">
        <div class="a-fixed-right-grid"><div class="a-fixed-right-grid-inner" style="padding-right:300px">
            <div class="a-fixed-right-grid-col a-col-left" style="padding-right:3.5%;float:left;">



                            <div data-a-input-name="service_type" class="a-checkbox service_type_checkbox"><label><input type="checkbox" name="service_type" value="JP_NEXT_DAY.DOMESTIC"><i class="a-icon a-icon-checkbox"></i><span class="a-label a-checkbox-label"></span><div class="overlay" style="display: inline;"></div></label></div>



                <div style="display: inline-block; vertical-align: top;">
                    <span class="a-size-medium checkbox_label a-text-bold">
                        次日达


                    </span>

                        <div class="service_type_header_shipmethods"></div>

                </div>




                    <span id="premiumShippingCutoffTime" class="service_type_header_tips"> 订单截止时间 2:00 p.m. </span>
























<span class="tiny a-size-base">
    <a href="javascript:openModalDialog('/gp/help/201503640', 550, 550, 'scrollbars=yes', null, 'yes')">



                了解更多信息


    </a>
</span>




                <span class="constraintMessageNode"></span>
                <div class="a-section errorMessageSection">
                    <div id="JP_NEXT_DAY.DOMESTIC_dependency_rule_error_message" class="a-box a-alert-inline a-alert-inline-error dependency_rule_error_message error_message" role="alert"><div class="a-box-inner a-alert-container"><i class="a-icon a-icon-alert" aria-hidden="true"></i><div class="a-alert-content"></div></div></div>
                </div>
            </div>
            <div class="a-text-right a-fixed-right-grid-col prime_eligibility_text_for_IN a-col-right" style="width:300px;margin-right:-300px;float:left;">

            </div>
        </div></div>
    </div></div>







  <div id="customized_delivery_rules_box~JP_NEXT_DAY.DOMESTIC"></div>

  <div class="a-box-group scheduledTimeWindowBox container_init_hidden">
    <div class="a-box a-box-normal scheduledTimeWindowSettings"><div class="a-box-inner a-padding-none">
    </div></div>
  </div>




 <div class="a-box-group configRulesBox container_init_hidden">


    <div class="a-box a-box-title a-text-left addRegionsBox" style="display: none;"><div class="a-box-inner">
      <div class="a-section sbr_fixed_column_row">
        <span></span>
        <div class="a-section a-spacing-none sbr_fixed_column requirementsMessageBox">
        </div>
        <div class="a-section a-spacing-none sbr_fixed_column alertMessageBox">
        </div>
        <br>
        <div class="a-section a-spacing-none sbr_fixed_column warningMessageBox">
        </div>
      </div>

    </div></div>

    <spamanual-transittime service-type="JP_NEXT_DAY.DOMESTIC"><div></div></spamanual-transittime>
     <div class="a-box a-box-normal configRulesContent"><div class="a-box-inner a-padding-none">
      <table class="a-bordered a-vertical-stripes configRulesTable">
        <tbody><tr class="heading"><th class="a-text-left a-align-center primeColumn">
            符合 Prime 资格
          </th>

          <th class="a-text-left a-align-center regionsColumn">
            区域
          </th>


          <th class="a-text-left a-align-center shippingTimeColumn">




            <span>送达时间</span>
            <br>
            <span class="shippingTimeDesc">

            包括处理时间

            </span>
          </th>


          <th class="a-text-left a-align-center shippingFeeColumn_shipment_based">
            运费






<br>





          </th>


          <th class="a-text-left a-align-center actionsColumn">

            <div class="a-section a-spacing-none">
              <span class="actions">操作</span>
            </div>

          </th></tr>
      <tr class="nonPrimeRule last_child"><td class="a-text-left a-align-top primeColumn">
    <div class="a-section prime_section">

    </div>
</td>

<td class="a-align-top">

  <div class="a-row a-grid-vertical-align a-grid-center regions_name">
    <div class="a-section regions_name_left">







                <span>广岛县, 冈山县, 岛根县, 鸟取县, 山口县, 爱知县, 岐阜县, 三重县, 静冈县, 福冈县, 鹿儿岛县, 熊本县, 宫崎县, 长崎县, 大分县, 佐贺县, 长野县, 新泻县, 秋田县, 青森县, 岩手县, 福井县, 石川县, 富山县, 福岛县, 宫城县, 山形县, 爱媛县, 香川县, 高知县, 德岛县, 千叶县, 群马县, 茨城县, 神奈川县, 埼玉县, 栃木县, 东京都, 山梨县, 兵库县, 京都府, 奈良县, 大阪府, 滋贺县, 和歌山县</span>


    </div>

    <div class="a-section regions_name_right">
      <a id="edit_0" class="a-link-normal editRegions" href="javascript:void(0)">编辑</a>
    </div>

  </div>
</td>





<td class="a-text-left a-align-top shippingTime">


  <span>
      1 个工作日
  </span>

</td>















<td class="shippingFee">








<div name="shippingFeePerOrderDiv">
    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>


                 <input type="text" value="0" name="pricePerOrder" class="a-input-text">


    </div></div>
    <span>每个订单</span>
</div>

<div name="shippingFeePlusDiv" class="aok-block">
    <div class="a-divider a-divider-break a-spacing-medium a-spacing-top-medium"><h5 aria-level="5">加</h5></div>

    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>

        <input type="text" value="0" name="unitPrice" class="a-input-text">

    </div></div>



        <span> 每  </span>



      <span class="a-dropdown-container"><select name="unitMeasure" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


        <option value="Per Item"> 商品 </option>



        <option value="Per Kilo" selected=""> Kg </option>


      </select><span tabindex="-1" data-a-class="unitMeasure" class="a-button a-button-dropdown unitMeasure" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">Kg</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>



</div>

</td>











<td class="a-text-left a-align-top">

  <a id="delete_0" class="a-link-normal deleteConfigRule" href="javascript:void(0)">
    删除
  </a>

</td></tr></tbody></table>
    </div></div>


    <div class="a-box addNewRuleButtonContainer"><div class="a-box-inner"><span id="JP_NEXT_DAY.DOMESTIC_addRuleButton" class="a-button a-button-primary addRegions"><span class="a-button-inner"><button id="JP_NEXT_DAY.DOMESTIC_addRuleButton-announce" class="a-button-text" type="button">
    添加新区域
  </button></span></span><div class="overlay" style="display: none;"></div></div></div>

  </div>





</div></div><div><div id="JP_SECOND_DAY.DOMESTIC" class="a-box-group a-spacing-top-base ">
    <div class="a-box a-box-title a-text-left serviceTypeBox"><div class="a-box-inner">
        <div class="a-fixed-right-grid"><div class="a-fixed-right-grid-inner" style="padding-right:300px">
            <div class="a-fixed-right-grid-col a-col-left" style="padding-right:3.5%;float:left;">



                            <div data-a-input-name="service_type" class="a-checkbox service_type_checkbox"><label><input type="checkbox" name="service_type" value="JP_SECOND_DAY.DOMESTIC"><i class="a-icon a-icon-checkbox"></i><span class="a-label a-checkbox-label"></span><div class="overlay" style="display: inline;"></div></label></div>



                <div style="display: inline-block; vertical-align: top;">
                    <span class="a-size-medium checkbox_label a-text-bold">
                        隔日达


                    </span>

                        <div class="service_type_header_shipmethods"></div>

                </div>




                    <span id="premiumShippingCutoffTime" class="service_type_header_tips"> 订单截止时间 2:00 p.m. </span>
























<span class="tiny a-size-base">
    <a href="javascript:openModalDialog('/gp/help/201503640', 550, 550, 'scrollbars=yes', null, 'yes')">



                了解更多信息


    </a>
</span>




                <span class="constraintMessageNode"></span>
                <div class="a-section errorMessageSection">
                    <div id="JP_SECOND_DAY.DOMESTIC_dependency_rule_error_message" class="a-box a-alert-inline a-alert-inline-error dependency_rule_error_message error_message" role="alert"><div class="a-box-inner a-alert-container"><i class="a-icon a-icon-alert" aria-hidden="true"></i><div class="a-alert-content"></div></div></div>
                </div>
            </div>
            <div class="a-text-right a-fixed-right-grid-col prime_eligibility_text_for_IN a-col-right" style="width:300px;margin-right:-300px;float:left;">

            </div>
        </div></div>
    </div></div>







  <div id="customized_delivery_rules_box~JP_SECOND_DAY.DOMESTIC"></div>

  <div class="a-box-group scheduledTimeWindowBox container_init_hidden">
    <div class="a-box a-box-normal scheduledTimeWindowSettings"><div class="a-box-inner a-padding-none">
    </div></div>
  </div>




 <div class="a-box-group configRulesBox container_init_hidden">


    <div class="a-box a-box-title a-text-left addRegionsBox" style="display: none;"><div class="a-box-inner">
      <div class="a-section sbr_fixed_column_row">
        <span></span>
        <div class="a-section a-spacing-none sbr_fixed_column requirementsMessageBox">
        </div>
        <div class="a-section a-spacing-none sbr_fixed_column alertMessageBox">
        </div>
        <br>
        <div class="a-section a-spacing-none sbr_fixed_column warningMessageBox">
        </div>
      </div>

    </div></div>

    <spamanual-transittime service-type="JP_SECOND_DAY.DOMESTIC"><div></div></spamanual-transittime>
     <div class="a-box a-box-normal configRulesContent"><div class="a-box-inner a-padding-none">
      <table class="a-bordered a-vertical-stripes configRulesTable">
        <tbody><tr class="heading"><th class="a-text-left a-align-center primeColumn">
            符合 Prime 资格
          </th>

          <th class="a-text-left a-align-center regionsColumn">
            区域
          </th>


          <th class="a-text-left a-align-center shippingTimeColumn">




            <span>送达时间</span>
            <br>
            <span class="shippingTimeDesc">

            包括处理时间

            </span>
          </th>


          <th class="a-text-left a-align-center shippingFeeColumn_shipment_based">
            运费






<br>





          </th>


          <th class="a-text-left a-align-center actionsColumn">

            <div class="a-section a-spacing-none">
              <span class="actions">操作</span>
            </div>

          </th></tr>
      <tr class="nonPrimeRule last_child"><td class="a-text-left a-align-top primeColumn">
    <div class="a-section prime_section">

    </div>
</td>

<td class="a-align-top">

  <div class="a-row a-grid-vertical-align a-grid-center regions_name">
    <div class="a-section regions_name_left">







                <span>北海道</span>


    </div>

    <div class="a-section regions_name_right">
      <a id="edit_0" class="a-link-normal editRegions" href="javascript:void(0)">编辑</a>
    </div>

  </div>
</td>





<td class="a-text-left a-align-top shippingTime">


  <span>
      2 天
  </span>

</td>















<td class="shippingFee">








<div name="shippingFeePerOrderDiv">
    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>


                 <input type="text" value="0" name="pricePerOrder" class="a-input-text">


    </div></div>
    <span>每个订单</span>
</div>

<div name="shippingFeePlusDiv" class="aok-block">
    <div class="a-divider a-divider-break a-spacing-medium a-spacing-top-medium"><h5 aria-level="5">加</h5></div>

    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>

        <input type="text" value="0" name="unitPrice" class="a-input-text">

    </div></div>



        <span> 每  </span>



      <span class="a-dropdown-container"><select name="unitMeasure" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


        <option value="Per Item"> 商品 </option>



        <option value="Per Kilo" selected=""> Kg </option>


      </select><span tabindex="-1" data-a-class="unitMeasure" class="a-button a-button-dropdown unitMeasure" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">Kg</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>



</div>

</td>











<td class="a-text-left a-align-top">

  <a id="delete_0" class="a-link-normal deleteConfigRule" href="javascript:void(0)">
    删除
  </a>

</td></tr></tbody></table>
    </div></div>


    <div class="a-box addNewRuleButtonContainer"><div class="a-box-inner"><span id="JP_SECOND_DAY.DOMESTIC_addRuleButton" class="a-button a-button-primary addRegions"><span class="a-button-inner"><button id="JP_SECOND_DAY.DOMESTIC_addRuleButton-announce" class="a-button-text" type="button">
    添加新区域
  </button></span></span><div class="overlay" style="display: none;"></div></div></div>

  </div>





</div></div><div><div id="JP_SAME_DAY.DOMESTIC" class="a-box-group a-spacing-top-base container_init_hidden">
    <div class="a-box a-box-title a-text-left serviceTypeBox"><div class="a-box-inner">
        <div class="a-fixed-right-grid"><div class="a-fixed-right-grid-inner" style="padding-right:300px">
            <div class="a-fixed-right-grid-col a-col-left" style="padding-right:3.5%;float:left;">



                            <div data-a-input-name="service_type" class="a-checkbox service_type_checkbox"><label><input type="checkbox" name="service_type" value="JP_SAME_DAY.DOMESTIC"><i class="a-icon a-icon-checkbox"></i><span class="a-label a-checkbox-label"></span><div class="overlay" style="display: inline;"></div></label></div>



                <div style="display: inline-block; vertical-align: top;">
                    <span class="a-size-medium checkbox_label a-text-bold">
                        当天送达


                    </span>

                        <div class="service_type_header_shipmethods"></div>

                </div>


                <span id="handlingTime" class="service_type_header_tips">   </span>


                <span class="constraintMessageNode"></span>
                <div class="a-section errorMessageSection">
                    <div id="JP_SAME_DAY.DOMESTIC_dependency_rule_error_message" class="a-box a-alert-inline a-alert-inline-error dependency_rule_error_message error_message" role="alert"><div class="a-box-inner a-alert-container"><i class="a-icon a-icon-alert" aria-hidden="true"></i><div class="a-alert-content"></div></div></div>
                </div>
            </div>
            <div class="a-text-right a-fixed-right-grid-col prime_eligibility_text_for_IN a-col-right" style="width:300px;margin-right:-300px;float:left;">

            </div>
        </div></div>
    </div></div>







  <div id="customized_delivery_rules_box~JP_SAME_DAY.DOMESTIC"></div>

  <div class="a-box-group scheduledTimeWindowBox container_init_hidden">
    <div class="a-box a-box-normal scheduledTimeWindowSettings"><div class="a-box-inner a-padding-none">
    </div></div>
  </div>




 <div class="a-box-group configRulesBox container_init_hidden">


    <div class="a-box a-box-title a-text-left addRegionsBox" style="display: none;"><div class="a-box-inner">
      <div class="a-section sbr_fixed_column_row">
        <span></span>
        <div class="a-section a-spacing-none sbr_fixed_column requirementsMessageBox">
        </div>
        <div class="a-section a-spacing-none sbr_fixed_column alertMessageBox">
        </div>
        <br>
        <div class="a-section a-spacing-none sbr_fixed_column warningMessageBox">
        </div>
      </div>

    </div></div>

    <spamanual-transittime service-type="JP_SAME_DAY.DOMESTIC"><div></div></spamanual-transittime>
     <div class="a-box a-box-normal configRulesContent"><div class="a-box-inner a-padding-none">
      <table class="a-bordered a-vertical-stripes configRulesTable">
        <tbody><tr class="heading last_child"><th class="a-text-left a-align-center primeColumn">
            符合 Prime 资格
          </th>

          <th class="a-text-left a-align-center regionsColumn">
            区域
          </th>


          <th class="a-text-left a-align-center shippingTimeColumn">




            <span>运输时间</span>
            <br>
            <span class="shippingTimeDesc">

            不包括备货时间

            </span>
          </th>


          <th class="a-text-left a-align-center shippingFeeColumn_shipment_based">
            运费






<br>





          </th>


          <th class="a-text-left a-align-center actionsColumn">

            <div class="a-section a-spacing-none">
              <span class="actions">操作</span>
            </div>

          </th></tr>
      </tbody></table>
    </div></div>


    <div class="a-box addNewRuleButtonContainer"><div class="a-box-inner"><span id="JP_SAME_DAY.DOMESTIC_addRuleButton" class="a-button a-button-primary addRegions"><span class="a-button-inner"><button id="JP_SAME_DAY.DOMESTIC_addRuleButton-announce" class="a-button-text" type="button">
    添加新区域
  </button></span></span><div class="overlay" style="display: none;"></div></div></div>

  </div>





</div></div><div><div id="JP_SCHEDULED.DOMESTIC" class="a-box-group a-spacing-top-base ">
    <div class="a-box a-box-title a-text-left serviceTypeBox"><div class="a-box-inner">
        <div class="a-fixed-right-grid"><div class="a-fixed-right-grid-inner" style="padding-right:300px">
            <div class="a-fixed-right-grid-col a-col-left" style="padding-right:3.5%;float:left;">



                            <div data-a-input-name="service_type" class="a-checkbox service_type_checkbox"><label><input type="checkbox" name="service_type" value="JP_SCHEDULED.DOMESTIC"><i class="a-icon a-icon-checkbox"></i><span class="a-label a-checkbox-label"></span><div class="overlay" style="display: inline;"></div></label></div>



                <div style="display: inline-block; vertical-align: top;">
                    <span class="a-size-medium checkbox_label a-text-bold">
                        计划配送


                    </span>

                        <div class="service_type_header_shipmethods"></div>

                </div>


                <span id="handlingTime" class="service_type_header_tips">   </span>


                <span class="constraintMessageNode"></span>
                <div class="a-section errorMessageSection">
                    <div id="JP_SCHEDULED.DOMESTIC_dependency_rule_error_message" class="a-box a-alert-inline a-alert-inline-error dependency_rule_error_message error_message" role="alert"><div class="a-box-inner a-alert-container"><i class="a-icon a-icon-alert" aria-hidden="true"></i><div class="a-alert-content"></div></div></div>
                </div>
            </div>
            <div class="a-text-right a-fixed-right-grid-col prime_eligibility_text_for_IN a-col-right" style="width:300px;margin-right:-300px;float:left;">

            </div>
        </div></div>
    </div></div>







  <div id="customized_delivery_rules_box~JP_SCHEDULED.DOMESTIC"></div>

  <div class="a-box-group scheduledTimeWindowBox container_init_hidden">
    <div class="a-box a-box-normal scheduledTimeWindowSettings"><div class="a-section scheduledTimeWindowOutter">
    <div class="a-box scheduledRequirementsMessageBox" style="display: none;"><div class="a-box-inner"></div></div>
    <table class="a-bordered a-vertical-stripes scheduledTimeWindowTable">
        <tbody><tr>
            <th class="a-text-left">
                <div class="a-section slotTypeSelection">
                    <div class="a-section slotTypeSelectionLeft">
                        <span class="a-text-bold">
                            配送时段类型:
                        </span>
                    </div>
                    <div class="a-section slotTypeSelectionRight">
                        <span class="selectedSlotTypeWrapper"><span class="a-declarative" data-action="a-popover" data-a-popover="{&quot;name&quot;:&quot;slotTypesPreloadContent&quot;,&quot;width&quot;:&quot;480&quot;,&quot;position&quot;:&quot;triggerHorizontal&quot;,&quot;closeButton&quot;:&quot;false&quot;}">
  <a href="javascript:void(0)" role="button" class="a-popover-trigger a-declarative">
      <span class="selectedSlotTypeText"> 时间时段 </span>
  <i class="a-icon a-icon-popover"></i></a>
</span>
<div class="a-popover-preload" id="a-popover-slotTypesPreloadContent">
    <div class="a-section">
        <span class="a-text-bold">
            配送时段类型
        </span>
    </div>
    <div class="a-section scheduledSlotTypes">
        <div id="slotTypeAccordion" data-a-accordion-name="slotTypeAccordion" class="a-box-group a-accordion" role="">


                <div class="a-box a-accordion-active" data-a-accordion-row-name="accordion-row-TIME"><div class="a-box-inner a-accordion-row-container">
                    <div class="a-accordion-row-a11y"><h5 aria-level="5"><a data-action="a-accordion" class="a-accordion-row a-declarative a-accordion-sr" href="#" role="button" aria-expanded="true" aria-label=""><i class="a-icon a-accordion-radio a-icon-radio-active"></i><span class="a-heading-text">
                        <div class="a-row">
                          <div class="a-column a-span12">时间时段</div>
                        </div>
                    </span></a></h5></div>
                    <div class="a-accordion-inner">
                        <span class="a-color-tertiary">
                            买家在结账时看到一个选项，以选择具体日期和时间段来接受配送（例如，2016 年 11 月 23 日下午 2 点到下午 4 点之间）。时间时段可能从 30 分钟到 8 小时不等，具体取决于指定商城的业务政策和承运人功能以及计划中的卖家。
                        </span>
                    </div>
                </div></div>


        </div>
    </div>
</div></span>
                        <br>
                        <span class="selectedSlotTypeDesc">买家在结账时看到一个选项，以选择具体日期和时间段来接受配送（例如，2016 年 11 月 23 日下午 2 点到下午 4 点之间）。时间时段可能从 30 分钟到 8 小时不等，具体取决于指定商城的业务政策和承运人功能以及计划中的卖家。</span>
                    </div>
                </div>
            </th>
        </tr>
        <tr>
            <td>
                <div class="a-section nominatedDayConfiguration slotConfiguration DAY hidden">
                    <table class="a-normal">
                        <tbody><tr>
                            <td class="a-text-right">
                                选择配送天数:
                            </td>

                            <td>

                                <div data-a-input-name="nominatedDays" class="a-checkbox"><label><input type="checkbox" name="nominatedDays" value="Sunday"><i class="a-icon a-icon-checkbox"></i><span class="a-label a-checkbox-label">
                                </span></label></div>

                                <span class="checkbox_label"> 星期日</span>
                            </td>

                            <td>

                                <div data-a-input-name="nominatedDays" class="a-checkbox"><label><input type="checkbox" name="nominatedDays" value="Monday" checked=""><i class="a-icon a-icon-checkbox"></i><span class="a-label a-checkbox-label">
                                </span></label></div>

                                <span class="checkbox_label"> 星期一</span>
                            </td>

                            <td>

                                <div data-a-input-name="nominatedDays" class="a-checkbox"><label><input type="checkbox" name="nominatedDays" value="Tuesday" checked=""><i class="a-icon a-icon-checkbox"></i><span class="a-label a-checkbox-label">
                                </span></label></div>

                                <span class="checkbox_label"> 星期二</span>
                            </td>

                            <td>

                                <div data-a-input-name="nominatedDays" class="a-checkbox"><label><input type="checkbox" name="nominatedDays" value="Wednesday" checked=""><i class="a-icon a-icon-checkbox"></i><span class="a-label a-checkbox-label">
                                </span></label></div>

                                <span class="checkbox_label"> 星期三</span>
                            </td>

                            <td>

                                <div data-a-input-name="nominatedDays" class="a-checkbox"><label><input type="checkbox" name="nominatedDays" value="Thursday" checked=""><i class="a-icon a-icon-checkbox"></i><span class="a-label a-checkbox-label">
                                </span></label></div>

                                <span class="checkbox_label"> 星期四</span>
                            </td>

                            <td>

                                <div data-a-input-name="nominatedDays" class="a-checkbox"><label><input type="checkbox" name="nominatedDays" value="Friday" checked=""><i class="a-icon a-icon-checkbox"></i><span class="a-label a-checkbox-label">
                                </span></label></div>

                                <span class="checkbox_label"> 星期五</span>
                            </td>

                            <td>

                                <div data-a-input-name="nominatedDays" class="a-checkbox"><label><input type="checkbox" name="nominatedDays" value="Saturday"><i class="a-icon a-icon-checkbox"></i><span class="a-label a-checkbox-label">
                                </span></label></div>

                                <span class="checkbox_label"> 星期六</span>
                            </td>

                        </tr>
                    </tbody></table>
                </div>
                <div class="a-section a-padding-small timeWindowConfiguration slotConfiguration TIME"><div id="deliveryWindowErrorMessage" class="a-section errorMessageSection"></div>
  <div id="deliveryWindowEditor" class="a-section">
    <div id="businessHoursHolder" class="a-section a-spacing-mini">


        <span> 时段选项: </span>
        <span class="a-dropdown-container"><select name="slotOption" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


               <option value="DEFAULT" selected=""> 默认 </option>



               <option value="YAMATO"> Yamato </option>



               <option value="JPPOST"> 日本邮政 </option>



               <option value="SAGAWA"> Sagawa </option>


        </select><span tabindex="-1" class="a-button a-button-dropdown a-button-small" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">默认</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>

      <span> 工作时间: </span>

      <span class="a-dropdown-container"><select name="businessHoursStart" autocomplete="off" role="combobox" id="businessHoursStart" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative" disabled=""></select><span tabindex="-1" class="a-button a-button-dropdown a-button-small a-button-disabled" aria-hidden="true" aria-disabled="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">8: 00</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>

      <span> 至 </span>

      <span class="a-dropdown-container"><select name="businessHoursEnd" autocomplete="off" role="combobox" id="businessHoursEnd" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative" disabled=""></select><span tabindex="-1" class="a-button a-button-dropdown a-button-small a-button-disabled" aria-hidden="true" aria-disabled="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">20: 00</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>


      <span id="deliveryWindowAddButton" class="a-button a-button-disabled a-button-primary a-button-small a-button-width-normal"><span class="a-button-inner"><button id="deliveryWindowAddButton-announce" disabled="" class="a-button-text a-text-center" type="button">
        添加配送窗口
      </button></span></span>

    </div>
    <span id="calendarHolder"><div class="windowEditorWrapper sevenday"><div class="windowEditorBackground"><div class="weekdayDisplay"><div class="day0">星期日</div><div class="day1">星期一</div><div class="day2">星期二</div><div class="day3">星期三</div><div class="day4">星期四</div><div class="day5">星期五</div><div class="day6 last-child">星期六</div></div><div class="windowEditorHour child0"><div class="daySeparators"><div class="day0"><div class="day1"><div class="day2"><div class="day3"><div class="day4"><div class="day5"><div class="day6"></div></div></div></div></div></div></div></div><div class="timeDisplay"><div class="innerTimeDisplay">8: 00</div></div><div class="bottomTimeDisplay"><div class="innerBottomTimeDisplay">9: 00</div></div></div><div class="windowEditorHour child1"><div class="daySeparators"><div class="day0"><div class="day1"><div class="day2"><div class="day3"><div class="day4"><div class="day5"><div class="day6"></div></div></div></div></div></div></div></div><div class="timeDisplay"><div class="innerTimeDisplay">9: 00</div></div><div class="bottomTimeDisplay"><div class="innerBottomTimeDisplay">10: 00</div></div></div><div class="windowEditorHour child2"><div class="daySeparators"><div class="day0"><div class="day1"><div class="day2"><div class="day3"><div class="day4"><div class="day5"><div class="day6"></div></div></div></div></div></div></div></div><div class="timeDisplay"><div class="innerTimeDisplay">10: 00</div></div><div class="bottomTimeDisplay"><div class="innerBottomTimeDisplay">11: 00</div></div></div><div class="windowEditorHour child3"><div class="daySeparators"><div class="day0"><div class="day1"><div class="day2"><div class="day3"><div class="day4"><div class="day5"><div class="day6"></div></div></div></div></div></div></div></div><div class="timeDisplay"><div class="innerTimeDisplay">11: 00</div></div><div class="bottomTimeDisplay"><div class="innerBottomTimeDisplay">12: 00</div></div></div><div class="windowEditorHour child4"><div class="daySeparators"><div class="day0"><div class="day1"><div class="day2"><div class="day3"><div class="day4"><div class="day5"><div class="day6"></div></div></div></div></div></div></div></div><div class="timeDisplay"><div class="innerTimeDisplay">12: 00</div></div><div class="bottomTimeDisplay"><div class="innerBottomTimeDisplay">13: 00</div></div></div><div class="windowEditorHour child5"><div class="daySeparators"><div class="day0"><div class="day1"><div class="day2"><div class="day3"><div class="day4"><div class="day5"><div class="day6"></div></div></div></div></div></div></div></div><div class="timeDisplay"><div class="innerTimeDisplay">13: 00</div></div><div class="bottomTimeDisplay"><div class="innerBottomTimeDisplay">14: 00</div></div></div><div class="windowEditorHour child6"><div class="daySeparators"><div class="day0"><div class="day1"><div class="day2"><div class="day3"><div class="day4"><div class="day5"><div class="day6"></div></div></div></div></div></div></div></div><div class="timeDisplay"><div class="innerTimeDisplay">14: 00</div></div><div class="bottomTimeDisplay"><div class="innerBottomTimeDisplay">15: 00</div></div></div><div class="windowEditorHour child7"><div class="daySeparators"><div class="day0"><div class="day1"><div class="day2"><div class="day3"><div class="day4"><div class="day5"><div class="day6"></div></div></div></div></div></div></div></div><div class="timeDisplay"><div class="innerTimeDisplay">15: 00</div></div><div class="bottomTimeDisplay"><div class="innerBottomTimeDisplay">16: 00</div></div></div><div class="windowEditorHour child8"><div class="daySeparators"><div class="day0"><div class="day1"><div class="day2"><div class="day3"><div class="day4"><div class="day5"><div class="day6"></div></div></div></div></div></div></div></div><div class="timeDisplay"><div class="innerTimeDisplay">16: 00</div></div><div class="bottomTimeDisplay"><div class="innerBottomTimeDisplay">17: 00</div></div></div><div class="windowEditorHour child9"><div class="daySeparators"><div class="day0"><div class="day1"><div class="day2"><div class="day3"><div class="day4"><div class="day5"><div class="day6"></div></div></div></div></div></div></div></div><div class="timeDisplay"><div class="innerTimeDisplay">17: 00</div></div><div class="bottomTimeDisplay"><div class="innerBottomTimeDisplay">18: 00</div></div></div><div class="windowEditorHour child10"><div class="daySeparators"><div class="day0"><div class="day1"><div class="day2"><div class="day3"><div class="day4"><div class="day5"><div class="day6"></div></div></div></div></div></div></div></div><div class="timeDisplay"><div class="innerTimeDisplay">18: 00</div></div><div class="bottomTimeDisplay"><div class="innerBottomTimeDisplay">19: 00</div></div></div><div class="windowEditorHour child11 last-child"><div class="daySeparators"><div class="day0"><div class="day1"><div class="day2"><div class="day3"><div class="day4"><div class="day5"><div class="day6"></div></div></div></div></div></div></div></div><div class="timeDisplay"><div class="innerTimeDisplay">19: 00</div></div><div class="bottomTimeDisplay"><div class="innerBottomTimeDisplay">20: 00</div></div></div></div><div class="windowEditorTopper"><div class="deliveryWindow hourstart0 hours4 daystart0 days7 unclickable"><span class="deliveryWindowDesc">星期日 - 星期六, 8:00 - 12:00</span></div><div class="deliveryWindow hourstart6 hours2 daystart0 days7 unclickable"><span class="deliveryWindowDesc">星期日 - 星期六, 14:00 - 16:00</span></div><div class="deliveryWindow hourstart8 hours2 daystart0 days7 unclickable"><span class="deliveryWindowDesc">星期日 - 星期六, 16:00 - 18:00</span></div><div class="deliveryWindow hourstart10 hours2 daystart0 days7 unclickable"><span class="deliveryWindowDesc">星期日 - 星期六, 18:00 - 20:00</span></div></div></div></span>
  </div></div>
            </td>
        </tr>
    </tbody></table>
</div></div>
  </div>




 <div class="a-box-group configRulesBox container_init_hidden">


    <div class="a-box a-box-title a-text-left addRegionsBox" style="display: none;"><div class="a-box-inner">
      <div class="a-section sbr_fixed_column_row">
        <span></span>
        <div class="a-section a-spacing-none sbr_fixed_column requirementsMessageBox">
        </div>
        <div class="a-section a-spacing-none sbr_fixed_column alertMessageBox">
        </div>
        <br>
        <div class="a-section a-spacing-none sbr_fixed_column warningMessageBox">
        </div>
      </div>

    </div></div>

    <spamanual-transittime service-type="JP_SCHEDULED.DOMESTIC"><div></div></spamanual-transittime>
     <div class="a-box a-box-normal configRulesContent"><div class="a-box-inner a-padding-none">
      <table class="a-bordered a-vertical-stripes configRulesTable">
        <tbody><tr class="heading"><th class="a-text-left a-align-center primeColumn">
符合 Prime 资格
</th>

<th class="a-text-left a-align-center regionsColumn">
区域
</th>



<th class="a-text-left a-align-center scheduleSelectionColumn" style="width:210px;">
可用配送天数







<span class="a-declarative" data-action="a-popover" data-a-popover="{&quot;name&quot;:&quot;scheduleDescriptionPopoverContent&quot;,&quot;width&quot;:&quot;210&quot;,&quot;position&quot;:&quot;triggerTop&quot;}" id="scheduleDescriptionPopover">
    <img alt="" src="https://images-fe.ssl-images-amazon.com/images/G/01/shipping-settings/exclamation_mark._V504880771_.png" class="scheduleDescription">
</span>

<div class="a-popover-preload" id="a-popover-scheduleDescriptionPopoverContent">
    <div class="a-section">
        <span class="a-color-tertiary scheduleSelectionDescription">自订单日期后买家可以安排配送的最小和最大天数。</span>
    </div>
</div>

</th>



      <th class="a-text-left a-align-center shippingFeeColumn_shipment_based">
        运费






<br>


      </th>



      <th class="a-text-left a-align-center actionsColumn">

        <div class="a-section a-spacing-none">
          <span class="actions">操作</span>
        </div>

      </th></tr>
      <tr class="nonPrimeRule"><td class="a-text-left a-align-top primeColumn">
    <div class="a-section prime_section">

    </div>
</td><td class="a-align-top">

  <div class="a-row a-grid-vertical-align a-grid-center regions_name">
    <div class="a-section regions_name_left">
      <span>广岛县, 冈山县, 岛根县, 鸟取县, 山口县, 爱知县, 岐阜县, 三重县, 静冈县, 长野县, 新泻县, 秋田县, 青森县, 岩手县, 福井县, 石川县, 富山县, 福岛县, 宫城县, 山形县, 千叶县, 群马县, 茨城县, 神奈川县, 埼玉县, 栃木县, 东京都, 山梨县, 兵库县, 京都府, 奈良县, 大阪府, 滋贺县, 和歌山县</span>
    </div>

    <div class="a-section regions_name_right">
      <a id="edit_0" class="a-link-normal editRegions" href="javascript:void(0)">编辑</a>
    </div>

  </div>
</td><td class="scheduleSelect">
    <div class="a-section">

            <span class="a-dropdown-container"><select name="scheduleMinDay" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">



            <option value="4" selected=""> 4 </option>



            <option value="5"> 5 </option>



            <option value="6"> 6 </option>



            <option value="7"> 7 </option>



            <option value="8"> 8 </option>



            <option value="9"> 9 </option>



            <option value="10"> 10 </option>



            <option value="11"> 11 </option>



            <option value="12"> 12 </option>



            <option value="13"> 13 </option>



            <option value="14"> 14 </option>



            <option value="15"> 15 </option>



            <option value="16"> 16 </option>



            <option value="17"> 17 </option>



            <option value="18"> 18 </option>



            </select><span tabindex="-1" data-a-class="minDaySelection" class="a-button a-button-dropdown minDaySelection" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">4</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>

        <span class="scheduleMinMaxBetweenLabel">至</span>

            <span class="a-dropdown-container"><select name="scheduleMaxDay" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">



            <option value="19" selected=""> 19 </option>



            <option value="20"> 20 </option>



            <option value="21"> 21 </option>



            <option value="22"> 22 </option>



            <option value="23"> 23 </option>



            <option value="24"> 24 </option>



            <option value="25"> 25 </option>



            <option value="26"> 26 </option>



            <option value="27"> 27 </option>



            <option value="28"> 28 </option>



            <option value="29"> 29 </option>



            <option value="30"> 30 </option>



            </select><span tabindex="-1" data-a-class="maxDaySelection" class="a-button a-button-dropdown maxDaySelection" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">19</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>

        <span class="scheduleMinMaxAfterLabel">天</span>
    </div>
</td><td class="shippingFee">








<div name="shippingFeePerOrderDiv">
    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>


                 <input type="text" value="0" name="pricePerOrder" class="a-input-text">


    </div></div>
    <span>每个订单</span>
</div>

<div name="shippingFeePlusDiv" class="aok-block">
    <div class="a-divider a-divider-break a-spacing-medium a-spacing-top-medium"><h5 aria-level="5">加</h5></div>

    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>

        <input type="text" value="0" name="unitPrice" class="a-input-text">

    </div></div>



        <span> 每  </span>



      <span class="a-dropdown-container"><select name="unitMeasure" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


        <option value="Per Item"> 商品 </option>



        <option value="Per Kilo" selected=""> Kg </option>


      </select><span tabindex="-1" data-a-class="unitMeasure" class="a-button a-button-dropdown unitMeasure" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">Kg</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>



</div>

</td><td class="a-text-left a-align-top">

  <a id="delete_0" class="a-link-normal deleteConfigRule" href="javascript:void(0)">
    删除
  </a>

</td></tr><tr class="nonPrimeRule"><td class="a-text-left a-align-top primeColumn">
    <div class="a-section prime_section">

    </div>
</td><td class="a-align-top">

  <div class="a-row a-grid-vertical-align a-grid-center regions_name">
    <div class="a-section regions_name_left">
      <span>北海道</span>
    </div>

    <div class="a-section regions_name_right">
      <a id="edit_1" class="a-link-normal editRegions" href="javascript:void(0)">编辑</a>
    </div>

  </div>
</td><td class="scheduleSelect">
    <div class="a-section">

            <span class="a-dropdown-container"><select name="scheduleMinDay" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">



            <option value="4" selected=""> 4 </option>



            <option value="5"> 5 </option>



            <option value="6"> 6 </option>



            <option value="7"> 7 </option>



            <option value="8"> 8 </option>



            <option value="9"> 9 </option>



            <option value="10"> 10 </option>



            <option value="11"> 11 </option>



            <option value="12"> 12 </option>



            <option value="13"> 13 </option>



            <option value="14"> 14 </option>



            <option value="15"> 15 </option>



            <option value="16"> 16 </option>



            <option value="17"> 17 </option>



            <option value="18"> 18 </option>



            </select><span tabindex="-1" data-a-class="minDaySelection" class="a-button a-button-dropdown minDaySelection" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">4</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>

        <span class="scheduleMinMaxBetweenLabel">至</span>

            <span class="a-dropdown-container"><select name="scheduleMaxDay" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">



            <option value="19" selected=""> 19 </option>



            <option value="20"> 20 </option>



            <option value="21"> 21 </option>



            <option value="22"> 22 </option>



            <option value="23"> 23 </option>



            <option value="24"> 24 </option>



            <option value="25"> 25 </option>



            <option value="26"> 26 </option>



            <option value="27"> 27 </option>



            <option value="28"> 28 </option>



            <option value="29"> 29 </option>



            <option value="30"> 30 </option>



            </select><span tabindex="-1" data-a-class="maxDaySelection" class="a-button a-button-dropdown maxDaySelection" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">19</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>

        <span class="scheduleMinMaxAfterLabel">天</span>
    </div>
</td><td class="shippingFee">








<div name="shippingFeePerOrderDiv">
    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>


                 <input type="text" value="0" name="pricePerOrder" class="a-input-text">


    </div></div>
    <span>每个订单</span>
</div>

<div name="shippingFeePlusDiv" class="aok-block">
    <div class="a-divider a-divider-break a-spacing-medium a-spacing-top-medium"><h5 aria-level="5">加</h5></div>

    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>

        <input type="text" value="0" name="unitPrice" class="a-input-text">

    </div></div>



        <span> 每  </span>



      <span class="a-dropdown-container"><select name="unitMeasure" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


        <option value="Per Item"> 商品 </option>



        <option value="Per Kilo" selected=""> Kg </option>


      </select><span tabindex="-1" data-a-class="unitMeasure" class="a-button a-button-dropdown unitMeasure" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">Kg</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>



</div>

</td><td class="a-text-left a-align-top">

  <a id="delete_1" class="a-link-normal deleteConfigRule" href="javascript:void(0)">
    删除
  </a>

</td></tr><tr class="nonPrimeRule"><td class="a-text-left a-align-top primeColumn">
    <div class="a-section prime_section">

    </div>
</td><td class="a-align-top">

  <div class="a-row a-grid-vertical-align a-grid-center regions_name">
    <div class="a-section regions_name_left">
      <span>福冈县, 鹿儿岛县, 熊本县, 宫崎县, 长崎县, 大分县, 佐贺县, 爱媛县, 香川县, 高知县, 德岛县</span>
    </div>

    <div class="a-section regions_name_right">
      <a id="edit_2" class="a-link-normal editRegions" href="javascript:void(0)">编辑</a>
    </div>

  </div>
</td><td class="scheduleSelect">
    <div class="a-section">

            <span class="a-dropdown-container"><select name="scheduleMinDay" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">



            <option value="4" selected=""> 4 </option>



            <option value="5"> 5 </option>



            <option value="6"> 6 </option>



            <option value="7"> 7 </option>



            <option value="8"> 8 </option>



            <option value="9"> 9 </option>



            <option value="10"> 10 </option>



            <option value="11"> 11 </option>



            <option value="12"> 12 </option>



            <option value="13"> 13 </option>



            <option value="14"> 14 </option>



            <option value="15"> 15 </option>



            <option value="16"> 16 </option>



            <option value="17"> 17 </option>



            <option value="18"> 18 </option>



            </select><span tabindex="-1" data-a-class="minDaySelection" class="a-button a-button-dropdown minDaySelection" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">4</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>

        <span class="scheduleMinMaxBetweenLabel">至</span>

            <span class="a-dropdown-container"><select name="scheduleMaxDay" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">



            <option value="19" selected=""> 19 </option>



            <option value="20"> 20 </option>



            <option value="21"> 21 </option>



            <option value="22"> 22 </option>



            <option value="23"> 23 </option>



            <option value="24"> 24 </option>



            <option value="25"> 25 </option>



            <option value="26"> 26 </option>



            <option value="27"> 27 </option>



            <option value="28"> 28 </option>



            <option value="29"> 29 </option>



            <option value="30"> 30 </option>



            </select><span tabindex="-1" data-a-class="maxDaySelection" class="a-button a-button-dropdown maxDaySelection" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">19</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>

        <span class="scheduleMinMaxAfterLabel">天</span>
    </div>
</td><td class="shippingFee">








<div name="shippingFeePerOrderDiv">
    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>


                 <input type="text" value="0" name="pricePerOrder" class="a-input-text">


    </div></div>
    <span>每个订单</span>
</div>

<div name="shippingFeePlusDiv" class="aok-block">
    <div class="a-divider a-divider-break a-spacing-medium a-spacing-top-medium"><h5 aria-level="5">加</h5></div>

    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>

        <input type="text" value="0" name="unitPrice" class="a-input-text">

    </div></div>



        <span> 每  </span>



      <span class="a-dropdown-container"><select name="unitMeasure" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


        <option value="Per Item"> 商品 </option>



        <option value="Per Kilo" selected=""> Kg </option>


      </select><span tabindex="-1" data-a-class="unitMeasure" class="a-button a-button-dropdown unitMeasure" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">Kg</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>



</div>

</td><td class="a-text-left a-align-top">

  <a id="delete_2" class="a-link-normal deleteConfigRule" href="javascript:void(0)">
    删除
  </a>

</td></tr><tr class="nonPrimeRule last_child"><td class="a-text-left a-align-top primeColumn">
    <div class="a-section prime_section">

    </div>
</td><td class="a-align-top">

  <div class="a-row a-grid-vertical-align a-grid-center regions_name">
    <div class="a-section regions_name_left">
      <span>冲绳县</span>
    </div>

    <div class="a-section regions_name_right">
      <a id="edit_3" class="a-link-normal editRegions" href="javascript:void(0)">编辑</a>
    </div>

  </div>
</td><td class="scheduleSelect">
    <div class="a-section">

            <span class="a-dropdown-container"><select name="scheduleMinDay" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">



            <option value="4" selected=""> 4 </option>



            <option value="5"> 5 </option>



            <option value="6"> 6 </option>



            <option value="7"> 7 </option>



            <option value="8"> 8 </option>



            <option value="9"> 9 </option>



            <option value="10"> 10 </option>



            <option value="11"> 11 </option>



            <option value="12"> 12 </option>



            <option value="13"> 13 </option>



            <option value="14"> 14 </option>



            <option value="15"> 15 </option>



            <option value="16"> 16 </option>



            <option value="17"> 17 </option>



            <option value="18"> 18 </option>



            </select><span tabindex="-1" data-a-class="minDaySelection" class="a-button a-button-dropdown minDaySelection" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">4</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>

        <span class="scheduleMinMaxBetweenLabel">至</span>

            <span class="a-dropdown-container"><select name="scheduleMaxDay" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">



            <option value="19" selected=""> 19 </option>



            <option value="20"> 20 </option>



            <option value="21"> 21 </option>



            <option value="22"> 22 </option>



            <option value="23"> 23 </option>



            <option value="24"> 24 </option>



            <option value="25"> 25 </option>



            <option value="26"> 26 </option>



            <option value="27"> 27 </option>



            <option value="28"> 28 </option>



            <option value="29"> 29 </option>



            <option value="30"> 30 </option>



            </select><span tabindex="-1" data-a-class="maxDaySelection" class="a-button a-button-dropdown maxDaySelection" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">19</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>

        <span class="scheduleMinMaxAfterLabel">天</span>
    </div>
</td><td class="shippingFee">








<div name="shippingFeePerOrderDiv">
    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>


                 <input type="text" value="0" name="pricePerOrder" class="a-input-text">


    </div></div>
    <span>每个订单</span>
</div>

<div name="shippingFeePlusDiv" class="aok-block">
    <div class="a-divider a-divider-break a-spacing-medium a-spacing-top-medium"><h5 aria-level="5">加</h5></div>

    <div class="a-input-text-addon-group-wrapper"><div class="a-input-text-addon-group templateInputGroup">
        <span class="a-input-text-addon">￥</span>

        <input type="text" value="0" name="unitPrice" class="a-input-text">

    </div></div>



        <span> 每  </span>



      <span class="a-dropdown-container"><select name="unitMeasure" autocomplete="off" role="combobox" tabindex="0" data-action="a-dropdown-select" class="a-native-dropdown a-declarative">


        <option value="Per Item"> 商品 </option>



        <option value="Per Kilo" selected=""> Kg </option>


      </select><span tabindex="-1" data-a-class="unitMeasure" class="a-button a-button-dropdown unitMeasure" aria-hidden="true"><span class="a-button-inner"><span class="a-button-text a-declarative" data-action="a-dropdown-button" aria-hidden="true"><span class="a-dropdown-prompt">Kg</span></span><i class="a-icon a-icon-dropdown"></i></span></span></span>



</div>

</td><td class="a-text-left a-align-top">

  <a id="delete_3" class="a-link-normal deleteConfigRule" href="javascript:void(0)">
    删除
  </a>

</td></tr></tbody></table>
    </div></div>


    <div class="a-box addNewRuleButtonContainer"><div class="a-box-inner"><span id="JP_SCHEDULED.DOMESTIC_addRuleButton" class="a-button a-button-primary addRegions"><span class="a-button-inner"><button id="JP_SCHEDULED.DOMESTIC_addRuleButton-announce" class="a-button-text" type="button">
    添加新区域
  </button></span></span><div class="overlay" style="display: none;"></div></div></div>

  </div>





</div></div></div></div>

</div></div>