('E:\\pycharm-project\\rpa-ziniao\\dist\\ShopSetup.exe',
 <PERSON>alse,
 False,
 False,
 'D:\\program\\python311\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-windowed.ico',
 None,
 True,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="requireAdministrator'
 b'" uiAccess="false"/>\n      </requestedPrivileges>\n    </security>\n  </tr'
 b'ustInfo>\n  <compatibility xmlns="urn:schemas-microsoft-com:compatibility'
 b'.v1">\n    <application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-'
 b'008deee3d3f0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a244022'
 b'5f93a}"/>\n      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"'
 b'/>\n      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n    '
 b'  <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </appli'
 b'cation>\n  </compatibility>\n  <application xmlns="urn:schemas-microsoft-c'
 b'om:asm.v3">\n    <windowsSettings>\n      <longPathAware xmlns="http://sch'
 b'emas.microsoft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </'
 b'windowsSettings>\n  </application>\n  <dependency>\n    <dependentAssembly>'
 b'\n      <assemblyIdentity type="win32" name="Microsoft.Windows.Common-Con'
 b'trols" version="6.0.0.0" processorArchitecture="*" publicKeyToken="6595b6414'
 b'4ccf1df" language="*"/>\n    </dependentAssembly>\n  </dependency>\n</assem'
 b'bly>',
 True,
 False,
 None,
 None,
 None,
 'E:\\pycharm-project\\rpa-ziniao\\build\\ShopSetup\\ShopSetup.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\pycharm-project\\rpa-ziniao\\build\\ShopSetup\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\pycharm-project\\rpa-ziniao\\build\\ShopSetup\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\pycharm-project\\rpa-ziniao\\build\\ShopSetup\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\pycharm-project\\rpa-ziniao\\build\\ShopSetup\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\pycharm-project\\rpa-ziniao\\build\\ShopSetup\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\pycharm-project\\rpa-ziniao\\build\\ShopSetup\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\program\\python311\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\program\\python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'D:\\program\\python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'D:\\program\\python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\program\\python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\program\\python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\program\\python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\program\\python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\program\\python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\program\\python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('shopSetup',
   'E:\\pycharm-project\\rpa-ziniao\\script\\shopSetup.py',
   'PYSOURCE'),
  ('pyarrow\\arrow_python_flight.dll',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_python_flight.dll',
   'BINARY'),
  ('pyarrow\\arrow_substrait.dll',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_substrait.dll',
   'BINARY'),
  ('pyarrow\\parquet.dll',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\parquet.dll',
   'BINARY'),
  ('pyarrow\\arrow_python.dll',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_python.dll',
   'BINARY'),
  ('pyarrow\\arrow.dll',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow.dll',
   'BINARY'),
  ('pyarrow\\arrow_flight.dll',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_flight.dll',
   'BINARY'),
  ('pyarrow\\arrow_python_parquet_encryption.dll',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_python_parquet_encryption.dll',
   'BINARY'),
  ('pyarrow\\arrow_acero.dll',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_acero.dll',
   'BINARY'),
  ('pyarrow\\arrow_dataset.dll',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_dataset.dll',
   'BINARY'),
  ('selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'D:\\program\\python311\\Lib\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'BINARY'),
  ('python311.dll', 'D:\\program\\python311\\python311.dll', 'BINARY'),
  ('pywin32_system32\\pythoncom311.dll',
   'D:\\program\\python311\\Lib\\site-packages\\pywin32_system32\\pythoncom311.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'D:\\program\\python311\\Lib\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'D:\\program\\python311\\Lib\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'BINARY'),
  ('select.pyd', 'D:\\program\\python311\\DLLs\\select.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\program\\python311\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\program\\python311\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\program\\python311\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\program\\python311\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\program\\python311\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\program\\python311\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\program\\python311\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\program\\python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'D:\\program\\python311\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\program\\python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\program\\python311\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'D:\\program\\python311\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32com\\shell\\shell.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\program\\python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32\\win32clipboard.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\win32\\win32clipboard.pyd',
   'EXTENSION'),
  ('win32\\win32process.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\win32\\win32process.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\program\\python311\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_tkinter.pyd', 'D:\\program\\python311\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\yaml\\_yaml.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\PIL\\_imagingft.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\program\\python311\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\writers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_compute.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_compute.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_substrait.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_substrait.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_s3fs.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_s3fs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_parquet_encryption.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_parquet_encryption.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_parquet.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_parquet.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_orc.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_orc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_json.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_json.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_hdfs.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_hdfs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_gcsfs.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_gcsfs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_fs.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_fs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_flight.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_flight.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_parquet_encryption.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_dataset_parquet_encryption.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_parquet.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_dataset_parquet.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_orc.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_dataset_orc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_dataset.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_csv.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_csv.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_acero.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_acero.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_feather.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_feather.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\lib.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\lib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'D:\\program\\python311\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\tslib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\testing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\sparse.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\sas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\reshape.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\properties.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\parsers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\ops.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\missing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\lib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\json.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\join.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\interval.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\internals.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\indexing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\index.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\hashing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\groupby.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\arrays.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\_libs\\algos.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\program\\python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\program\\python311\\VCRUNTIME140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\program\\python311\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('pyarrow.libs\\msvcp140-a118642f3ae8774fb9dc223e15c4a52e.dll',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow.libs\\msvcp140-a118642f3ae8774fb9dc223e15c4a52e.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'D:\\program\\python311\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll', 'D:\\program\\python311\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\program\\python311\\DLLs\\libffi-8.dll', 'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'D:\\program\\python311\\Lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('tcl86t.dll', 'D:\\program\\python311\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\program\\python311\\DLLs\\tk86t.dll', 'BINARY'),
  ('python3.dll', 'D:\\program\\python311\\python3.dll', 'BINARY'),
  ('sqlite3.dll', 'D:\\program\\python311\\DLLs\\sqlite3.dll', 'BINARY'),
  ('pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'D:\\program\\python311\\Lib\\site-packages\\pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'BINARY'),
  ('global_logger.py',
   'E:\\pycharm-project\\rpa-ziniao\\global_logger.py',
   'DATA'),
  ('gui_framework.py',
   'E:\\pycharm-project\\rpa-ziniao\\gui_framework.py',
   'DATA'),
  ('gui_utils.py', 'E:\\pycharm-project\\rpa-ziniao\\gui_utils.py', 'DATA'),
  ('ziniao_config.json',
   'E:\\pycharm-project\\rpa-ziniao\\ziniao_config.json',
   'DATA'),
  ('ziniao_rpa_base.py',
   'E:\\pycharm-project\\rpa-ziniao\\ziniao_rpa_base.py',
   'DATA'),
  ('ziniao_socket_client.py',
   'E:\\pycharm-project\\rpa-ziniao\\ziniao_socket_client.py',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:\\program\\python311\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\program\\python311\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:\\program\\python311\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\program\\python311\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\program\\python311\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:\\program\\python311\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'D:\\program\\python311\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'D:\\program\\python311\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tk_data\\tk.tcl', 'D:\\program\\python311\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tk_data\\tclIndex',
   'D:\\program\\python311\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'D:\\program\\python311\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tk_data\\text.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\program\\python311\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:\\program\\python311\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\program\\python311\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'D:\\program\\python311\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'D:\\program\\python311\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\program\\python311\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\program\\python311\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'D:\\program\\python311\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\program\\python311\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\program\\python311\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\program\\python311\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\program\\python311\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'D:\\program\\python311\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'D:\\program\\python311\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:\\program\\python311\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\program\\python311\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tm.tcl', 'D:\\program\\python311\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\program\\python311\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\program\\python311\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\program\\python311\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\program\\python311\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\program\\python311\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\program\\python311\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:\\program\\python311\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:\\program\\python311\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'D:\\program\\python311\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:\\program\\python311\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\program\\python311\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\program\\python311\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\program\\python311\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\crypto_factory.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\crypto_factory.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\platform.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_definitions.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_definitions.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\api.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\kms_client.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\kms_client.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\future.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\future.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\ordering.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\ordering.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\list_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\list_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\visibility.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\arrow_to_pandas.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\arrow_to_pandas.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\transport.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\transport.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitset_stack.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitset_stack.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\chunk_resolver.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\chunk_resolver.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\float16.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\float16.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\asof_join_node.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\asof_join_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\reader.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\debug.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\debug.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_encryption_key.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_encryption_key.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\diff.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\diff.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\tensor\\converter.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\tensor\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\json_simple.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\json_simple.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\local_wrap_kms_client.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\local_wrap_kms_client.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\README.md',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\README.md',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\common.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\common.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_key_unwrapper.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_key_unwrapper.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_reader.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_reader.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\windows_compatibility.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\windows_compatibility.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\xxhasher.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\xxhasher.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_avx2.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_avx2.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\test_encryption_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\test_encryption_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\test_common.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\data.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\data.h',
   'DATA'),
  ('pyarrow\\_azurefs.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_azurefs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\aggregate_node.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\aggregate_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\dlpack_abi.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\c\\dlpack_abi.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_tracing_middleware.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_tracing_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\hash_join_dict.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\hash_join_dict.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\json.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\extension\\json.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\map.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\map.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\concurrent_map.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\concurrent_map.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\time.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\time.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\iterators.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\iterators.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.test1.orc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.test1.orc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\macros.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\macros.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_convert.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_convert.h',
   'DATA'),
  ('pyarrow\\__init__.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\__init__.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension_type.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_toolkit.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_toolkit.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\partition.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\partition.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\api.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\size_statistics.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\size_statistics.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\parser.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\parser.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\string_builder.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\string_builder.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\async.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\async.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\io.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\api\\io.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_decimal.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\tpch_node.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\tpch_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\ree_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\ree_util.h',
   'DATA'),
  ('pyarrow\\_fs.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_fs.pxd',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_init.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_init.cc',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\extension_type.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\extension_type.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\scalar.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\scalar.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\reader.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\pch.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\pch.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\pch.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\pch.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_to_arrow.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_to_arrow.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_vector.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_vector.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\lib.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\tensor.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\tensor.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\result.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\result.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_cuda.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\libarrow_cuda.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\ieee.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\ieee.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\gcsfs.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\gcsfs.h',
   'DATA'),
  ('pyarrow\\arrow_python.lib',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_python.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\future_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\future_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_scalar_inline.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\visit_scalar_inline.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\config.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\config.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\uniform_real.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\uniform_real.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\logging.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\logging.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\visibility.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\filesystem_library.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\filesystem_library.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\executor_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\executor_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\filesystem.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\filesystem.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\datetime.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\datetime.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\stream_writer.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\stream_writer.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow_lib.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow_lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_substrait.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\libarrow_substrait.pxd',
   'DATA'),
  ('pyarrow\\error.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\error.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\csv.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\csv.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\orc\\options.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\orc\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\column_decoder.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\column_decoder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\fast-dtoa.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\fast-dtoa.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_nested.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_nested.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\cached-powers.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\cached-powers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow_api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow_api.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_comparison.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\level_comparison.h',
   'DATA'),
  ('pyarrow\\device.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\device.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\caching.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\caching.h',
   'DATA'),
  ('pyarrow\\io.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\io.pxi',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\decimal.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\python_to_arrow.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\python_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\uuid.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\extension\\uuid.h',
   'DATA'),
  ('pyarrow\\_compute.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_compute.pyx',
   'DATA'),
  ('pyarrow\\_acero.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_acero.pyx',
   'DATA'),
  ('pyarrow\\include\\parquet\\page_index.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\page_index.h',
   'DATA'),
  ('pyarrow\\benchmark.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\benchmark.pxi',
   'DATA'),
  ('pyarrow\\arrow.lib',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\expression.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\expression.h',
   'DATA'),
  ('pyarrow\\lib_api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\lib_api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\converter.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\converter.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\hasher.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\hasher.h',
   'DATA'),
  ('pyarrow\\_cuda.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_cuda.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\math.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\math.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\schema_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\schema_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_base.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_base.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\lib_api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\lib_api.h',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.all-named-index.parquet',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.all-named-index.parquet',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl_allocator.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\stl_allocator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\hash_join.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\hash_join.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\bloom_filter_reader.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\bloom_filter_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\crc32.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\crc32.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\util.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_key_wrapper.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_key_wrapper.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\base64.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\base64.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\file.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\file.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_writer.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\pch.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\pch.h',
   'DATA'),
  ('pyarrow\\_dataset_parquet_encryption.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_dataset_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow\\includes\\libgandiva.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\libgandiva.pxd',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow_api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow_api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\windows_fixup.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\windows_fixup.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_csv.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_csv.h',
   'DATA'),
  ('pyarrow\\_flight.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_flight.pyx',
   'DATA'),
  ('pyarrow\\_dataset_orc.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_dataset_orc.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\type_traits.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\compression.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\compression.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\test_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\pch.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\pch.h',
   'DATA'),
  ('pyarrow\\_cuda.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_cuda.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\value_parsing.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\value_parsing.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\printer.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\printer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\flight.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\flight.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\flight.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\flight.h',
   'DATA'),
  ('pyarrow\\includes\\libparquet_encryption.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\libparquet_encryption.pxd',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_page.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\column_page.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_init.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_init.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\query_context.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\query_context.h',
   'DATA'),
  ('pyarrow\\tests\\extensions.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\extensions.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\slow.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\slow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\serde.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\serde.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\xxhash\\xxhash.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\xxhash\\xxhash.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\chunked_array.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\chunked_array.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\double-to-string.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\double-to-string.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\hash_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\hash_util.h',
   'DATA'),
  ('pyarrow\\_acero.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_acero.pxd',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\writer.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\api\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\date.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\date.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_auth_handlers.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_auth_handlers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\pretty_print.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\pretty_print.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\type.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\type.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\datetime.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\datetime.cc',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pch.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_time.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_time.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\schema.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\schema.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_system_key_material_store.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_system_key_material_store.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\column_builder.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\column_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\ios.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\ios.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\strptime.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\strptime.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\udf.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\udf.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\abi.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\c\\abi.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\order_by_impl.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\order_by_impl.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\metadata.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\metadata.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\decimal.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\decimal.h',
   'DATA'),
  ('pyarrow\\memory.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\memory.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\bool8.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\extension\\bool8.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\hashing.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\hashing.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\align_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\align_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\udf.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\udf.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\message.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\message.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\function_options.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\function_options.h',
   'DATA'),
  ('pyarrow\\parquet.lib',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\parquet.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\test_common.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_flight_server.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_flight_server.h',
   'DATA'),
  ('pyarrow\\pandas-shim.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\pandas-shim.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\relation.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\relation.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\ProducerConsumerQueue.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\ProducerConsumerQueue.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_interop.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_interop.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_ipc.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_ipc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\ipc.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\ipc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\device.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\device.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_generate.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_generate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\test_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\test_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_to_arrow.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_to_arrow.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\helpers.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\helpers.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\int_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\int_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_adaptive.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_adaptive.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\key_value_metadata.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\key_value_metadata.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\kernel.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\kernel.h',
   'DATA'),
  ('pyarrow\\scalar.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\scalar.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\chunked_builder.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\chunked_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\dictionary.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\dictionary.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\converter.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\status.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\status.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\decimal.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\inference.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\inference.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\spaced.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\spaced.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_binary.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_binary.h',
   'DATA'),
  ('pyarrow\\_s3fs.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_s3fs.pyx',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\reader.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_uint128.hpp',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_uint128.hpp',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\ubsan.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\ubsan.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\test_nodes.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\test_nodes.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\stdio.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\stdio.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\orc\\adapter.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\orc\\adapter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\buffer_builder.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\buffer_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\path_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\path_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\options.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\options.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_python.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\libarrow_python.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\small_vector.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\small_vector.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\transform.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\transform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\union_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\union_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_auth.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_auth.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\extension_type.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\extension_type.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\filesystem.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\filesystem.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_parquet.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_parquet.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\test_common.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\io_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\io_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\cast.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\cast.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_dataset_parquet.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\libarrow_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\span.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\span.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\extension_type.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\extension_type.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\libarrow.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\endian.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\endian.h',
   'DATA'),
  ('pyarrow\\_dlpack.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_dlpack.pxi',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_material.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_material.h',
   'DATA'),
  ('pyarrow\\tests\\bound_function_visit_strings.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\bound_function_visit_strings.pyx',
   'DATA'),
  ('pyarrow\\_parquet.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_parquet.pxd',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\decimal.orc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\decimal.orc',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\gdb.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\gdb.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\decimal.jsn.gz',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\decimal.jsn.gz',
   'DATA'),
  ('pyarrow\\include\\parquet\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\extension_set.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\extension_set.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\vendored\\pythoncapi_compat.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\vendored\\pythoncapi_compat.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visitor_generate.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\visitor_generate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\matchers.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\matchers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\dict_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\dict_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_random.hpp',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_random.hpp',
   'DATA'),
  ('pyarrow\\lib.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\test_common.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\test_common.h',
   'DATA'),
  ('pyarrow\\_json.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_json.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\udf.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\udf.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\basic_decimal.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\basic_decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\filesystem.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\filesystem.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\localfs.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\localfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\invalid_row.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\invalid_row.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_scalar.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_scalar.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\dlpack.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\c\\dlpack.h',
   'DATA'),
  ('pyarrow\\compat.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\compat.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\hash_join_node.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\hash_join_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\scanner.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\scanner.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\statistics.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\statistics.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_array_inline.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\visit_array_inline.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\cpu_info.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\cpu_info.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\gdb.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\gdb.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\delimiting.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\delimiting.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\writer.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_neon.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_neon.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_block_counter.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_block_counter.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.orc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.orc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\visibility.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\benchmark_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum-dtoa.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum-dtoa.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\writer.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\buffered.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\buffered.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_data_inline.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\visit_data_inline.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\double_conversion.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\double_conversion.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\parser.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\parser.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\common.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\common.h',
   'DATA'),
  ('pyarrow\\lib.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\lib.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\type_traits.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\mutex.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\mutex.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\backpressure_handler.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\backpressure_handler.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\dataset.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\dataset.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\unreachable.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\unreachable.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_acero.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\libarrow_acero.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\compare.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compare.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\reader.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\reader.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_scanner.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\column_scanner.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\test_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\async_test_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\async_test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visitor.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\visitor.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_run_end.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_run_end.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_extras.hpp',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_extras.hpp',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\diy-fp.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\diy-fp.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\regex.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\regex.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\windows_compatibility.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\windows_compatibility.h',
   'DATA'),
  ('pyarrow\\arrow_dataset.lib',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_dataset.lib',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\io.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\io.cc',
   'DATA'),
  ('pyarrow\\builder.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\builder.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\hdfs.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\hdfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\pch.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\accumulation_queue.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\accumulation_queue.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\gdb.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\gdb.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\function.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\function.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\string-to-double.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\string-to-double.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_type_inline.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\visit_type_inline.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\utils.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\utils.h',
   'DATA'),
  ('pyarrow\\_parquet_encryption.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_parquet_encryption.pxd',
   'DATA'),
  ('pyarrow\\include\\parquet\\encoding.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encoding.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\helpers.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\options.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_json.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_json.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\builder.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\builder.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_dataset.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\libarrow_dataset.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\azurefs.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\azurefs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\types.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\types.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\io.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\io.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\inference.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\inference.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\aligned_storage.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\aligned_storage.h',
   'DATA'),
  ('pyarrow\\_substrait.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_substrait.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_internal.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_internal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\rows_to_batches.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\rows_to_batches.h',
   'DATA'),
  ('pyarrow\\array.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\array.pxi',
   'DATA'),
  ('pyarrow\\_hdfs.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_hdfs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\compare.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\compare.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.test1.jsn.gz',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.test1.jsn.gz',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\io.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\io.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\feather.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\feather.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\stl.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\time_series_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\time_series_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\visibility.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\visibility.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\filesystem.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\filesystem.cc',
   'DATA'),
  ('pyarrow\\includes\\__init__.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\__init__.pxd',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_metadata.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_metadata.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_util.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_feather.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\libarrow_feather.pxd',
   'DATA'),
  ('pyarrow\\includes\\libarrow_flight.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\libarrow_flight.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\mockfs.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\mockfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\visibility.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\visibility.h',
   'DATA'),
  ('pyarrow\\_dataset_parquet.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\parquet_encryption.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\parquet_encryption.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\helpers.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\c\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\type_fwd.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_pandas.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_pandas.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\math_constants.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\math_constants.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\interfaces.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\interfaces.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\parallel.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\parallel.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\decimal.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\decimal.cc',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\test_in_memory_kms.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\test_in_memory_kms.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\s3_test_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\s3_test_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\ipc.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\ipc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\mman.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\mman.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\gtest_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\gtest_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\plan.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\plan.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\encryption.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\encryption.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\middleware.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\middleware.h',
   'DATA'),
  ('pyarrow\\includes\\common.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\common.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\record_batch.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\record_batch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\visibility.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\random.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\random.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\counting_semaphore.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\counting_semaphore.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\file_writer.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\file_writer.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_comparison_inc.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\level_comparison_inc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\generator.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\generator.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\benchmark.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\benchmark.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_visit.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_visit.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_test.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_test.cc',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\CMakeLists.txt',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\CMakeLists.txt',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_to_arrow.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\double-conversion.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\double-conversion.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\visibility.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\extension_types.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\extension_types.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\queue.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\queue.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_reader.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\column_reader.h',
   'DATA'),
  ('pyarrow\\table.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\table.pxi',
   'DATA'),
  ('pyarrow\\_gcsfs.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_gcsfs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_base.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_base.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\device_allocation_type_set.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\device_allocation_type_set.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\transport_server.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\transport_server.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\uri.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\uri.h',
   'DATA'),
  ('pyarrow\\_csv.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_csv.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_union.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_union.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\schema.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\api\\schema.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\benchmark.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\benchmark.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_decimal.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_dict.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_dict.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\visibility.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\visibility.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\vendored\\CMakeLists.txt',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\vendored\\CMakeLists.txt',
   'DATA'),
  ('pyarrow\\arrow_acero.lib',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_acero.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_orc.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_orc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_base.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_base.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\tdigest.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\tdigest.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\compressed.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\compressed.h',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.parquet',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.parquet',
   'DATA'),
  ('pyarrow\\_parquet_encryption.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_conversion_inc.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\level_conversion_inc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\config.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\config.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\memory.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\memory.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\bloom_filter.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\bloom_filter.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\csv.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\csv.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\writer.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\algorithm.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\algorithm.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\datetime.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\datetime.h',
   'DATA'),
  ('pyarrow\\_orc.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_orc.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_nested.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_nested.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_generator.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_generator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\dispatch.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\dispatch.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\two_level_cache_with_expiration.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\two_level_cache_with_expiration.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_to_arrow.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_to_arrow.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl_iterator.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\stl_iterator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_builders.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_builders.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\fixed-dtoa.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\fixed-dtoa.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\registry.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\registry.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\opaque.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\extension\\opaque.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\concurrency.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\concurrency.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\platform.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\platform.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_middleware.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_middleware.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\parquet_encryption.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\parquet_encryption.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\range.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\range.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_middleware.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_middleware.h',
   'DATA'),
  ('pyarrow\\_dataset.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_dataset.pyx',
   'DATA'),
  ('pyarrow\\_feather.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_feather.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\process.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\process.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\kms_client_factory.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\kms_client_factory.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\iterator.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\iterator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\prefetch.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\prefetch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\binary_view_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\binary_view_util.h',
   'DATA'),
  ('pyarrow\\ipc.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\ipc.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\windows_fixup.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\windows_fixup.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\schema.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\schema.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\ipc.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\ipc.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\table_builder.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\table_builder.h',
   'DATA'),
  ('pyarrow\\tensor.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tensor.pxi',
   'DATA'),
  ('pyarrow\\_dataset.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_dataset.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\common.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\memory.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\memory.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\int_util_overflow.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\int_util_overflow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\projector.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\projector.h',
   'DATA'),
  ('pyarrow\\arrow_python_parquet_encryption.lib',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_python_parquet_encryption.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\memory_pool.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\memory_pool.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\task_group.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\task_group.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\exception.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\exception.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_pandas.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_pandas.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\launder.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\launder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\platform.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\buffer.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\buffer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_auth.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_auth.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_default.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_default.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\strtod.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\strtod.h',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.column-metadata-handling.parquet',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.column-metadata-handling.parquet',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\concatenate.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\concatenate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\sparse_tensor.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\sparse_tensor.h',
   'DATA'),
  ('pyarrow\\config.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\config.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\s3fs.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\s3fs.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\test_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\fixed_shape_tensor.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\extension\\fixed_shape_tensor.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\iterators.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\iterators.h',
   'DATA'),
  ('pyarrow\\_json.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_json.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\chunker.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\chunker.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\converter.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\byte_size.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\byte_size.h',
   'DATA'),
  ('pyarrow\\_parquet.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_parquet.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_util.h',
   'DATA'),
  ('pyarrow\\types.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\types.pxi',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.jsn.gz',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.jsn.gz',
   'DATA'),
  ('pyarrow\\lib.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\lib.pyx',
   'DATA'),
  ('pyarrow\\tests\\pyarrow_cython_example.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\pyarrow_cython_example.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_dict.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_dict.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\file_reader.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\file_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\portable-snippets\\safe-math.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\portable-snippets\\safe-math.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\object_parser.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\object_parser.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\types_async.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\types_async.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\memory_pool_test.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\memory_pool_test.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\parquet_encryption.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\parquet_encryption.cc',
   'DATA'),
  ('pyarrow\\_pyarrow_cpp_tests.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_pyarrow_cpp_tests.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_interop.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_interop.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\tz.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\tz.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\bloom_filter.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\bloom_filter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\options.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\options.h',
   'DATA'),
  ('pyarrow\\tests\\data\\feather\\v0.17.0.version.2-compression.lz4.feather',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\feather\\v0.17.0.version.2-compression.lz4.feather',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\test_common.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\functional.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\functional.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\parquet_encryption_config.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\parquet_encryption_config.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\string.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\string.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.orc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.orc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\vector.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\vector.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\parquet_version.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\parquet_version.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\cancel.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\cancel.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\options.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\options.h',
   'DATA'),
  ('pyarrow\\_dataset_parquet.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_dataset_parquet.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\helpers.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\async.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\async.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\print.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\print.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\bridge.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\c\\bridge.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\formatting.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\formatting.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking64_default.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking64_default.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\pch.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\pch.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_python_internal.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_python_internal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\gtest_compat.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\gtest_compat.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\properties.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\properties.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\benchmark_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\vendored\\pythoncapi_compat.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\vendored\\pythoncapi_compat.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_conversion.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\level_conversion.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\python_test.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\python_test.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\map_node.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\map_node.h',
   'DATA'),
  ('pyarrow\\public-api.pxi',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\public-api.pxi',
   'DATA'),
  ('pyarrow\\_csv.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_csv.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\benchmark.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\benchmark.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_tracing_middleware.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_tracing_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_run_end.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_run_end.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\stopwatch.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\stopwatch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\row\\grouper.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\row\\grouper.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\extension_type.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\tensorflow\\convert.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\tensorflow\\convert.h',
   'DATA'),
  ('pyarrow\\_compute.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_compute.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_generator_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_generator_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\tz_private.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\tz_private.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\dataset_writer.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\dataset_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\rapidjson_defs.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\rapidjson_defs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\exec_plan.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\exec_plan.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_test.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_test.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\test_plan_builder.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\test_plan_builder.h',
   'DATA'),
  ('pyarrow\\arrow_python_flight.lib',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_python_flight.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\statistics.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\statistics.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\sort.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\sort.h',
   'DATA'),
  ('pyarrow\\arrow_substrait.lib',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_substrait.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\task_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\task_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\csv.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\csv.cc',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_key_material_store.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_key_material_store.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_to_arrow.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_to_arrow.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\logger.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\logger.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_run_reader.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_run_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\visibility.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\xxhash.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\xxhash.h',
   'DATA'),
  ('pyarrow\\_fs.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_fs.pyx',
   'DATA'),
  ('pyarrow\\includes\\libarrow_fs.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\includes\\libarrow_fs.pxd',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_writer.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\column_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\otel_logging.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\otel_logging.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\builder.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_primitive.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_primitive.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\types.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\types.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_primitive.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_primitive.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\inference.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\inference.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\table.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\table.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\object_writer.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\object_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\tracing.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\tracing.h',
   'DATA'),
  ('pyarrow\\gandiva.pyx',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\gandiva.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_convert.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_convert.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\partition_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\partition_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\pcg_random.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\pcg_random.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_ops.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_ops.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\datum.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\datum.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\benchmark_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.some-named-index.parquet',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.some-named-index.parquet',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\flight.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\flight.cc',
   'DATA'),
  ('pyarrow\\_pyarrow_cpp_tests.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_pyarrow_cpp_tests.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\thread_pool.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\thread_pool.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\validate.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\validate.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\platform.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_binary.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_binary.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\type_traits.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\utf8.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\utf8.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow_lib.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow_lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\discovery.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\discovery.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_aggregate.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_aggregate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\api.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\api.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_convert.cc',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_convert.cc',
   'DATA'),
  ('pyarrow\\include\\parquet\\stream_reader.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\stream_reader.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.jsn.gz',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.jsn.gz',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_init.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_init.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\pch.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\type_traits.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\type_traits.h',
   'DATA'),
  ('pyarrow\\arrow_flight.lib',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\arrow_flight.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_avx512.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_avx512.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_cookie_middleware.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_cookie_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\fixed_width_test_util.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\fixed_width_test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\portable-snippets\\debug-trap.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\portable-snippets\\debug-trap.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\chunker.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\chunker.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pch.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\trie.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\trie.h',
   'DATA'),
  ('pyarrow\\_orc.pxd',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\_orc.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\hdfs.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\hdfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\options.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\options.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\type_fwd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\checked_cast.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\checked_cast.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\reader.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\parquet\\api\\reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\simd.h',
   'D:\\program\\python311\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\simd.h',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'D:\\program\\python311\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\program\\python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\program\\python311\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\program\\python311\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'D:\\program\\python311\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'D:\\program\\python311\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\METADATA',
   'D:\\program\\python311\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\program\\python311\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\WHEEL',
   'D:\\program\\python311\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\RECORD',
   'D:\\program\\python311\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\INSTALLER',
   'D:\\program\\python311\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\INSTALLER',
   'DATA'),
  ('selenium\\webdriver\\common\\macos\\selenium-manager',
   'D:\\program\\python311\\Lib\\site-packages\\selenium\\webdriver\\common\\macos\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'D:\\program\\python311\\Lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('selenium\\py.typed',
   'D:\\program\\python311\\Lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v129\\py.typed',
   'D:\\program\\python311\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v129\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v85\\py.typed',
   'D:\\program\\python311\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v85\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v130\\py.typed',
   'D:\\program\\python311\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v130\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'D:\\program\\python311\\Lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'D:\\program\\python311\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('selenium\\webdriver\\common\\linux\\selenium-manager',
   'D:\\program\\python311\\Lib\\site-packages\\selenium\\webdriver\\common\\linux\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v131\\py.typed',
   'D:\\program\\python311\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v131\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'D:\\program\\python311\\Lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'D:\\program\\python311\\Lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'D:\\program\\python311\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'D:\\program\\python311\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\WHEEL',
   'D:\\program\\python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\top_level.txt',
   'D:\\program\\python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\REQUESTED',
   'D:\\program\\python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\INSTALLER',
   'D:\\program\\python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\METADATA',
   'D:\\program\\python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'D:\\program\\python311\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\RECORD',
   'D:\\program\\python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'D:\\program\\python311\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'D:\\program\\python311\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\LICENSE',
   'D:\\program\\python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\LICENSE',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'D:\\program\\python311\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\entry_points.txt',
   'D:\\program\\python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\entry_points.txt',
   'DATA'),
  ('base_library.zip',
   'E:\\pycharm-project\\rpa-ziniao\\build\\ShopSetup\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 **********,
 [('runw.exe',
   'D:\\program\\python311\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'D:\\program\\python311\\python311.dll')
