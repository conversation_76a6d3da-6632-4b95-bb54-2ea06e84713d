# macOS 构建包 - 店铺设置工具

## 📋 概述
这是一个完整的macOS构建包，包含在Mac上生成可执行文件所需的所有文件和脚本。

## 🛠️ 系统要求
- macOS 10.14 或更高版本
- Python 3.8+ (推荐使用 Homebrew 安装)
- Xcode Command Line Tools
- 稳定的网络连接

## 📦 包含文件

### 核心源码文件
```
script/
├── shopSetup.py              # 主程序
├── ziniao_rpa_base.py         # RPA基础类
├── gui_framework.py           # GUI框架
├── gui_utils.py               # GUI工具
├── global_logger.py           # 日志系统
├── ziniao_socket_client.py    # Socket客户端
└── ziniao_config.json         # 配置文件
```

### 构建脚本
```
build_cross_platform.py       # 跨平台构建脚本
build_macos.sh                # macOS一键构建脚本
requirements-build.txt         # 构建依赖
```

### 文档
```
BUILD_GUIDE.md                # 详细构建指南
macOS_Build_Package.md         # 本文档
README.txt                     # 使用说明
```

## 🚀 快速构建指南

### 步骤1：环境准备
```bash
# 安装Python (如果未安装)
brew install python3

# 验证Python版本
python3 --version  # 应该是 3.8+
```

### 步骤2：安装依赖
```bash
# 安装构建工具
pip3 install PyInstaller

# 安装项目依赖
pip3 install -r requirements-build.txt
```

### 步骤3：一键构建
```bash
# 给予执行权限
chmod +x build_macos.sh

# 执行构建
./build_macos.sh
```

### 步骤4：获取结果
构建完成后，您将得到：
```
ShopSetup_macOS/
├── 店铺设置                  # 主程序
├── 启动店铺设置.command       # 启动脚本
├── ziniao_config.json        # 配置文件
└── README.txt               # 使用说明
```

## 🔧 手动构建（高级用户）

### 详细构建命令
```bash
# 清理旧文件
rm -rf build dist *.spec

# 执行PyInstaller
pyinstaller \
    --clean \
    --noconfirm \
    --name=ShopSetup \
    --onefile \
    --windowed \
    --osx-bundle-identifier=com.ziniao.shopsetup \
    --add-data="ziniao_config.json:." \
    --add-data="gui_framework.py:." \
    --add-data="gui_utils.py:." \
    --add-data="ziniao_rpa_base.py:." \
    --add-data="ziniao_socket_client.py:." \
    --add-data="global_logger.py:." \
    --hidden-import=selenium \
    --hidden-import=tkinter \
    --hidden-import=tkinter.scrolledtext \
    --hidden-import=requests \
    --hidden-import=pandas \
    script/shopSetup.py

# 创建分发包
mkdir -p ShopSetup_macOS
cp dist/ShopSetup ShopSetup_macOS/店铺设置
cp ziniao_config.json ShopSetup_macOS/
chmod +x ShopSetup_macOS/店铺设置

# 创建启动脚本
cat > ShopSetup_macOS/启动店铺设置.command << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
./店铺设置
EOF
chmod +x ShopSetup_macOS/启动店铺设置.command
```

## 🐛 故障排除

### 常见问题

#### 1. Python版本问题
```bash
# 检查Python版本
python3 --version

# 如果版本过低，更新Python
brew upgrade python3
```

#### 2. PyInstaller安装失败
```bash
# 升级pip
python3 -m pip install --upgrade pip

# 重新安装PyInstaller
pip3 install --force-reinstall PyInstaller
```

#### 3. 权限问题
```bash
# 给予脚本执行权限
chmod +x build_macos.sh
chmod +x ShopSetup_macOS/店铺设置
chmod +x ShopSetup_macOS/启动店铺设置.command
```

#### 4. 依赖缺失
```bash
# 安装完整依赖
pip3 install selenium pyautogui requests pandas openpyxl

# 检查特定模块
python3 -c "import selenium; print('Selenium OK')"
python3 -c "import tkinter; print('Tkinter OK')"
```

### macOS特定问题

#### 1. Gatekeeper警告
首次运行时可能遇到安全警告：
1. 右键点击程序 → "打开"
2. 或在"系统偏好设置" → "安全性与隐私"中允许

#### 2. 权限请求
程序可能需要以下权限：
- 辅助功能权限（用于自动化）
- 屏幕录制权限（用于截图）

## 📋 构建清单

### 构建前检查
- [ ] macOS 10.14+ 系统
- [ ] Python 3.8+ 已安装
- [ ] PyInstaller 已安装
- [ ] 所有源码文件完整
- [ ] 网络连接正常

### 构建后验证
- [ ] 可执行文件生成成功
- [ ] 文件大小合理（约80-100MB）
- [ ] 启动脚本可执行
- [ ] 配置文件存在
- [ ] README文档完整

### 测试清单
- [ ] 程序能正常启动
- [ ] GUI界面显示正常
- [ ] 配置加载正常
- [ ] 基础功能可用

## 📞 技术支持

### 构建支持
如果遇到构建问题：
1. 检查Python和PyInstaller版本
2. 确认所有依赖已安装
3. 查看详细错误信息
4. 参考故障排除部分

### 联系方式
- 提供详细的错误日志
- 说明macOS版本和Python版本
- 描述具体的构建步骤和错误

---

**注意**: 这个构建包基于Windows版本的成功经验，包含了所有必要的修复和优化。在macOS上构建应该能够生成完全功能的可执行文件。
